# -*- coding: utf-8 -*-
"""
نماذج قاعدة البيانات لنظام المحاسبة
Database Models for Accounting System
"""

from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from decimal import Decimal
from werkzeug.security import generate_password_hash, check_password_hash
import os

db = SQLAlchemy()

class User(db.Model):
    """نموذج المستخدم - User Model"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    is_admin = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    
    # Relationships
    created_debts = db.relationship('Debt', backref='created_by', lazy=True, foreign_keys='Debt.created_by_id')
    created_remittances = db.relationship('Remittance', backref='created_by', lazy=True, foreign_keys='Remittance.created_by_id')
    updated_debts = db.relationship('Debt', backref='updated_by', lazy=True, foreign_keys='Debt.updated_by_id')
    updated_remittances = db.relationship('Remittance', backref='updated_by', lazy=True, foreign_keys='Remittance.updated_by_id')

    def set_password(self, password):
        """تشفير كلمة المرور"""
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        """التحقق من كلمة المرور"""
        return check_password_hash(self.password_hash, password)

    def to_dict(self):
        """تحويل النموذج إلى قاموس"""
        return {
            'id': self.id,
            'username': self.username,
            'name': self.name,
            'is_admin': self.is_admin,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }

class Sponsor(db.Model):
    """نموذج الراعي - Sponsor Model"""
    __tablename__ = 'sponsors'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, comment='اسم الراعي')
    phone = db.Column(db.String(20), comment='رقم الهاتف')
    address = db.Column(db.Text, comment='العنوان')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='تاريخ الإنشاء')
    
    # العلاقات - Relationships
    debts = db.relationship('Debt', backref='sponsor', lazy=True, cascade='all, delete-orphan')
    remittances = db.relationship('Remittance', backref='sponsor', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Sponsor {self.name}>'
    
    def to_dict(self):
        """تحويل النموذج إلى قاموس"""
        return {
            'id': self.id,
            'name': self.name,
            'phone': self.phone,
            'address': self.address,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'total_debts': self.get_total_debts(),
            'total_remittances': self.get_total_remittances(),
            'balance': self.get_balance()
        }
    
    def get_total_debts(self):
        """حساب إجمالي الديون"""
        total = db.session.query(db.func.sum(Debt.amount)).filter_by(sponsor_id=self.id).scalar()
        return float(total) if total else 0.0
    
    def get_total_remittances(self):
        """حساب إجمالي الحوالات"""
        total = db.session.query(db.func.sum(Remittance.amount)).filter_by(sponsor_id=self.id).scalar()
        return float(total) if total else 0.0
    
    def get_balance(self):
        """حساب الرصيد المتبقي (الديون - الحوالات)"""
        return self.get_total_debts() - self.get_total_remittances()


class Debt(db.Model):
    """نموذج الدين - Debt Model"""
    __tablename__ = 'debts'
    
    id = db.Column(db.Integer, primary_key=True)
    sponsor_id = db.Column(db.Integer, db.ForeignKey('sponsors.id'), nullable=False, comment='معرف الراعي')
    amount = db.Column(db.Numeric(15, 2), nullable=False, comment='المبلغ')
    description = db.Column(db.Text, comment='الوصف')
    date = db.Column(db.Date, nullable=False, comment='التاريخ')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='تاريخ الإنشاء')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='تاريخ التحديث')
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True, comment='معرف المستخدم الذي أنشأ السجل')
    updated_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True, comment='معرف آخر مستخدم قام بالتحديث')
    
    def __repr__(self):
        return f'<Debt {self.amount} for Sponsor {self.sponsor_id}>'
    
    def to_dict(self):
        """تحويل النموذج إلى قاموس"""
        return {
            'id': self.id,
            'sponsor_id': self.sponsor_id,
            'sponsor_name': self.sponsor.name if self.sponsor else None,
            'amount': float(self.amount),
            'description': self.description,
            'date': self.date.isoformat() if self.date else None,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }


class Remittance(db.Model):
    """نموذج الحوالة - Remittance Model"""
    __tablename__ = 'remittances'
    
    id = db.Column(db.Integer, primary_key=True)
    sponsor_id = db.Column(db.Integer, db.ForeignKey('sponsors.id'), nullable=False, comment='معرف الراعي')
    amount = db.Column(db.Numeric(15, 2), nullable=False, comment='المبلغ')
    source = db.Column(db.String(100), comment='مصدر الحوالة')
    date = db.Column(db.Date, nullable=False, comment='التاريخ')
    created_at = db.Column(db.DateTime, default=datetime.utcnow, comment='تاريخ الإنشاء')
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='تاريخ التحديث')
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True, comment='معرف المستخدم الذي أنشأ السجل')
    updated_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True, comment='معرف آخر مستخدم قام بالتحديث')
    
    def __repr__(self):
        return f'<Remittance {self.amount} for Sponsor {self.sponsor_id}>'
    
    def to_dict(self):
        """تحويل النموذج إلى قاموس"""
        return {
            'id': self.id,
            'sponsor_id': self.sponsor_id,
            'sponsor_name': self.sponsor.name if self.sponsor else None,
            'amount': float(self.amount),
            'source': self.source,
            'date': self.date.isoformat() if self.date else None,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
