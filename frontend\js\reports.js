// نظام المحاسبة للرعاة - التقارير المحسنة
// Enhanced Reports functionality for Accounting System

// متغيرات التقارير
let currentReportData = null;
let currentReportType = null;

// إعدادات التقارير
const reportSettings = {
    itemsPerPage: 10,
    sortColumn: null,
    sortDirection: 'asc',
    searchTerm: '',
    dateFilter: {
        from: null,
        to: null
    }
};

// تحسين عرض الملخص الإجمالي مع الرسوم البيانية
function displayEnhancedSummaryReport(data) {
    currentReportData = data;
    currentReportType = 'summary';
    
    let html = `
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    الملخص الإجمالي المحسن
                </h5>
                <div class="btn-group">
                    <button class="btn btn-outline-primary btn-sm" onclick="printEnhancedReport()">
                        <i class="fas fa-print me-2"></i>طباعة
                    </button>
                    <button class="btn btn-outline-success btn-sm" onclick="exportToPDF('summary')">
                        <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                    </button>
                    <button class="btn btn-outline-info btn-sm" onclick="showDateFilterModal()">
                        <i class="fas fa-filter me-2"></i>فلترة
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- الإحصائيات العامة المحسنة -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-card bg-primary text-white">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number">${data.totals.sponsors_count}</h3>
                                <p class="stat-label">إجمالي الرعاة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card bg-danger text-white">
                            <div class="stat-icon">
                                <i class="fas fa-minus-circle"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number">${formatCurrency(data.totals.total_debts)}</h3>
                                <p class="stat-label">إجمالي الديون</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card bg-success text-white">
                            <div class="stat-icon">
                                <i class="fas fa-plus-circle"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number">${formatCurrency(data.totals.total_remittances)}</h3>
                                <p class="stat-label">إجمالي الحوالات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card ${data.totals.total_balance >= 0 ? 'bg-warning' : 'bg-info'} text-white">
                            <div class="stat-icon">
                                <i class="fas fa-balance-scale"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number">${formatCurrency(data.totals.total_balance)}</h3>
                                <p class="stat-label">الرصيد المتبقي</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الرسوم البيانية -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="chart-container">
                            <h6 class="text-center mb-3">توزيع المبالغ</h6>
                            <div class="chart-wrapper">
                                <canvas id="summaryChart"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="chart-container">
                            <h6 class="text-center mb-3">مقارنة الرعاة (أعلى 10)</h6>
                            <div class="chart-wrapper">
                                <canvas id="sponsorsChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الإحصائيات المتقدمة -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card bg-light">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-chart-line me-2"></i>
                                    إحصائيات متقدمة
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="advanced-stat">
                                            <span class="stat-value">${formatCurrency(calculateAverageDebt(data.sponsors))}</span>
                                            <span class="stat-desc">متوسط الديون للراعي</span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="advanced-stat">
                                            <span class="stat-value">${formatCurrency(calculateAverageRemittance(data.sponsors))}</span>
                                            <span class="stat-desc">متوسط الحوالات للراعي</span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="advanced-stat">
                                            <span class="stat-value">${getHighestDebtSponsor(data.sponsors)}</span>
                                            <span class="stat-desc">أعلى راعي ديناً</span>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="advanced-stat">
                                            <span class="stat-value">${getHighestRemittanceSponsor(data.sponsors)}</span>
                                            <span class="stat-desc">أعلى راعي حوالات</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- جدول الرعاة المحسن -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">تفاصيل الرعاة</h6>
                                <div class="search-box">
                                    <input type="text" class="form-control form-control-sm" 
                                           placeholder="البحث في الرعاة..." 
                                           onkeyup="filterSponsorsTable(this.value)">
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover" id="sponsorsTable">
                                        <thead class="table-dark">
                                            <tr>
                                                <th onclick="sortTable('name')" class="sortable">
                                                    الراعي <i class="fas fa-sort"></i>
                                                </th>
                                                <th onclick="sortTable('total_debts')" class="sortable">
                                                    إجمالي الديون <i class="fas fa-sort"></i>
                                                </th>
                                                <th onclick="sortTable('total_remittances')" class="sortable">
                                                    إجمالي الحوالات <i class="fas fa-sort"></i>
                                                </th>
                                                <th onclick="sortTable('balance')" class="sortable">
                                                    الرصيد المتبقي <i class="fas fa-sort"></i>
                                                </th>
                                                <th>الحالة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="sponsorsTableBody">
                                        </tbody>
                                    </table>
                                </div>
                                <div class="d-flex justify-content-between align-items-center mt-3">
                                    <div class="pagination-info">
                                        <span id="paginationInfo"></span>
                                    </div>
                                    <nav>
                                        <ul class="pagination pagination-sm" id="pagination">
                                        </ul>
                                    </nav>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal فلترة التاريخ -->
        <div class="modal fade" id="dateFilterModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">فلترة حسب التاريخ</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="filterFromDate" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="filterFromDate">
                            </div>
                            <div class="col-md-6">
                                <label for="filterToDate" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="filterToDate">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-primary" onclick="applyDateFilter()">تطبيق الفلتر</button>
                        <button type="button" class="btn btn-outline-warning" onclick="clearDateFilter()">مسح الفلتر</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('report-content').innerHTML = html;
    
    // إنشاء الرسوم البيانية
    setTimeout(() => {
        createSummaryChart(data);
        createSponsorsChart(data);
        renderSponsorsTable(data.sponsors);
    }, 100);
}

// حساب متوسط الديون
function calculateAverageDebt(sponsors) {
    if (sponsors.length === 0) return 0;
    const total = sponsors.reduce((sum, sponsor) => sum + sponsor.total_debts, 0);
    return total / sponsors.length;
}

// حساب متوسط الحوالات
function calculateAverageRemittance(sponsors) {
    if (sponsors.length === 0) return 0;
    const total = sponsors.reduce((sum, sponsor) => sum + sponsor.total_remittances, 0);
    return total / sponsors.length;
}

// الحصول على أعلى راعي ديناً
function getHighestDebtSponsor(sponsors) {
    if (sponsors.length === 0) return 'لا يوجد';
    const highest = sponsors.reduce((max, sponsor) => 
        sponsor.total_debts > max.total_debts ? sponsor : max
    );
    return highest.name;
}

// الحصول على أعلى راعي حوالات
function getHighestRemittanceSponsor(sponsors) {
    if (sponsors.length === 0) return 'لا يوجد';
    const highest = sponsors.reduce((max, sponsor) => 
        sponsor.total_remittances > max.total_remittances ? sponsor : max
    );
    return highest.name;
}

// عرض جدول الرعاة مع التصفح
function renderSponsorsTable(sponsors, page = 1) {
    const filteredSponsors = filterSponsors(sponsors);
    const sortedSponsors = sortSponsors(filteredSponsors);
    const totalPages = Math.ceil(sortedSponsors.length / reportSettings.itemsPerPage);
    const startIndex = (page - 1) * reportSettings.itemsPerPage;
    const endIndex = startIndex + reportSettings.itemsPerPage;
    const pageSponsors = sortedSponsors.slice(startIndex, endIndex);
    
    let html = '';
    pageSponsors.forEach(sponsor => {
        const balanceClass = sponsor.balance > 0 ? 'text-danger' : sponsor.balance < 0 ? 'text-success' : 'text-muted';
        const statusBadge = sponsor.balance > 0 ? 
            '<span class="badge bg-danger">مدين</span>' : 
            sponsor.balance < 0 ? 
            '<span class="badge bg-success">دائن</span>' : 
            '<span class="badge bg-secondary">متوازن</span>';
        
        html += `
            <tr>
                <td><strong>${sponsor.name}</strong></td>
                <td class="text-danger text-currency">${formatCurrency(sponsor.total_debts)}</td>
                <td class="text-success text-currency">${formatCurrency(sponsor.total_remittances)}</td>
                <td class="${balanceClass} text-currency"><strong>${formatCurrency(sponsor.balance)}</strong></td>
                <td>${statusBadge}</td>
                <td>
                    <button class="btn btn-outline-info btn-sm" onclick="viewSponsorDetails(${sponsor.id})" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                </td>
            </tr>
        `;
    });
    
    document.getElementById('sponsorsTableBody').innerHTML = html;
    
    // تحديث معلومات التصفح
    const start = startIndex + 1;
    const end = Math.min(endIndex, sortedSponsors.length);
    document.getElementById('paginationInfo').textContent = 
        `عرض ${start} إلى ${end} من ${sortedSponsors.length} راعي`;
    
    // إنشاء أزرار التصفح
    renderPagination(page, totalPages);
}

// إنشاء أزرار التصفح
function renderPagination(currentPage, totalPages) {
    let html = '';
    
    // زر السابق
    html += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="renderSponsorsTable(currentReportData.sponsors, ${currentPage - 1})">السابق</a>
        </li>
    `;
    
    // أرقام الصفحات
    for (let i = 1; i <= totalPages; i++) {
        if (i === currentPage || i === 1 || i === totalPages || (i >= currentPage - 1 && i <= currentPage + 1)) {
            html += `
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="renderSponsorsTable(currentReportData.sponsors, ${i})">${i}</a>
                </li>
            `;
        } else if (i === currentPage - 2 || i === currentPage + 2) {
            html += '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
    }
    
    // زر التالي
    html += `
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="renderSponsorsTable(currentReportData.sponsors, ${currentPage + 1})">التالي</a>
        </li>
    `;
    
    document.getElementById('pagination').innerHTML = html;
}

// فلترة الرعاة حسب البحث والحالة
function filterSponsors(sponsors) {
    let filtered = sponsors;

    // فلترة حسب البحث
    if (reportSettings.searchTerm) {
        filtered = enhancedSearch(reportSettings.searchTerm, filtered);
    }

    // فلترة حسب الحالة
    if (typeof filterSponsorsByStatus === 'function') {
        filtered = filterSponsorsByStatus(filtered);
    }

    return filtered;
}

// ترتيب الرعاة
function sortSponsors(sponsors) {
    if (!reportSettings.sortColumn) return sponsors;

    return sponsors.sort((a, b) => {
        let aValue = a[reportSettings.sortColumn];
        let bValue = b[reportSettings.sortColumn];

        if (typeof aValue === 'string') {
            aValue = aValue.toLowerCase();
            bValue = bValue.toLowerCase();
        }

        if (reportSettings.sortDirection === 'asc') {
            return aValue > bValue ? 1 : -1;
        } else {
            return aValue < bValue ? 1 : -1;
        }
    });
}

// فلترة جدول الرعاة
function filterSponsorsTable(searchTerm) {
    reportSettings.searchTerm = searchTerm;
    renderSponsorsTable(currentReportData.sponsors, 1);
}

// ترتيب الجدول
function sortTable(column) {
    if (reportSettings.sortColumn === column) {
        reportSettings.sortDirection = reportSettings.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
        reportSettings.sortColumn = column;
        reportSettings.sortDirection = 'asc';
    }

    // تحديث أيقونات الترتيب
    document.querySelectorAll('.sortable i').forEach(icon => {
        icon.className = 'fas fa-sort';
    });

    const currentIcon = document.querySelector(`th[onclick="sortTable('${column}')"] i`);
    if (currentIcon) {
        currentIcon.className = reportSettings.sortDirection === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down';
    }

    renderSponsorsTable(currentReportData.sponsors, 1);
}

// عرض نموذج فلترة التاريخ
function showDateFilterModal() {
    const modal = new bootstrap.Modal(document.getElementById('dateFilterModal'));
    modal.show();
}

// تطبيق فلتر التاريخ
function applyDateFilter() {
    const fromDate = document.getElementById('filterFromDate').value;
    const toDate = document.getElementById('filterToDate').value;

    reportSettings.dateFilter.from = fromDate;
    reportSettings.dateFilter.to = toDate;

    // إعادة تحميل التقرير مع الفلتر
    loadFilteredSummaryReport();

    bootstrap.Modal.getInstance(document.getElementById('dateFilterModal')).hide();
}

// مسح فلتر التاريخ
function clearDateFilter() {
    reportSettings.dateFilter.from = null;
    reportSettings.dateFilter.to = null;

    document.getElementById('filterFromDate').value = '';
    document.getElementById('filterToDate').value = '';

    // إعادة تحميل التقرير بدون فلتر
    loadSummaryReport();

    bootstrap.Modal.getInstance(document.getElementById('dateFilterModal')).hide();
}

// تحميل التقرير مع الفلتر
async function loadFilteredSummaryReport() {
    try {
        showLoading('report-content');

        let url = `${API_BASE_URL}/reports/summary`;
        const params = new URLSearchParams();

        if (reportSettings.dateFilter.from) {
            params.append('from_date', reportSettings.dateFilter.from);
        }
        if (reportSettings.dateFilter.to) {
            params.append('to_date', reportSettings.dateFilter.to);
        }

        if (params.toString()) {
            url += '?' + params.toString();
        }

        const response = await fetch(url);
        const data = await response.json();

        if (data.success) {
            displayEnhancedSummaryReport(data.data);
        } else {
            showError('فشل في تحميل التقرير المفلتر');
        }
    } catch (error) {
        console.error('خطأ في تحميل التقرير المفلتر:', error);
        showError('خطأ في الاتصال بالخادم');
    }
}

// طباعة التقرير المحسن
function printEnhancedReport() {
    const reportContent = document.getElementById('report-content').innerHTML;
    const printWindow = window.open('', '_blank');

    printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>تقرير نظام المحاسبة المحسن</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
            <link href="css/style.css" rel="stylesheet">
            <style>
                @media print {
                    .btn, .search-box, .pagination { display: none !important; }
                    body { font-size: 12px; }
                    .chart-container { page-break-inside: avoid; }
                    .stat-card { margin-bottom: 10px; }
                    .card { border: 1px solid #ddd; margin-bottom: 20px; }
                }
                .stat-card {
                    padding: 15px;
                    border-radius: 8px;
                    margin-bottom: 15px;
                }
                .stat-icon {
                    font-size: 2rem;
                    opacity: 0.8;
                    float: right;
                    margin-left: 15px;
                }
                .stat-content h3 {
                    margin: 0;
                    font-size: 1.5rem;
                }
                .stat-content p {
                    margin: 0;
                    opacity: 0.9;
                }
            </style>
        </head>
        <body>
            <div class="container mt-3">
                <div class="text-center mb-4">
                    <h3>نظام المحاسبة للرعاة - التقرير المحسن</h3>
                    <p class="text-muted">تاريخ الطباعة: ${new Date().toLocaleDateString('ar-YE')}</p>
                </div>
                ${reportContent}
            </div>
        </body>
        </html>
    `);

    printWindow.document.close();
    printWindow.print();
}

// تصدير إلى PDF
function exportToPDF(reportType) {
    try {
        // التحقق من وجود مكتبة jsPDF
        if (typeof window.jsPDF === 'undefined') {
            alert('مكتبة PDF غير متوفرة. يرجى إعادة تحميل الصفحة.');
            return;
        }

        const { jsPDF } = window.jsPDF;
        const doc = new jsPDF({
            orientation: 'portrait',
            unit: 'mm',
            format: 'a4'
        });

        // إعداد الخط العربي (تقريبي)
        doc.setFont('helvetica');
        doc.setFontSize(16);

        // العنوان الرئيسي
        doc.text('نظام المحاسبة للرعاة', 105, 20, { align: 'center' });
        doc.setFontSize(12);
        doc.text(`تاريخ التقرير: ${new Date().toLocaleDateString('ar-YE')}`, 105, 30, { align: 'center' });

        let yPosition = 50;

        if (reportType === 'summary' && currentReportData) {
            // تصدير الملخص الإجمالي
            doc.setFontSize(14);
            doc.text('الملخص الإجمالي', 105, yPosition, { align: 'center' });
            yPosition += 15;

            // الإحصائيات العامة
            doc.setFontSize(10);
            const totals = currentReportData.totals;

            doc.text(`عدد الرعاة: ${totals.sponsors_count}`, 20, yPosition);
            yPosition += 8;
            doc.text(`إجمالي الديون: ${formatCurrency(totals.total_debts)}`, 20, yPosition);
            yPosition += 8;
            doc.text(`إجمالي الحوالات: ${formatCurrency(totals.total_remittances)}`, 20, yPosition);
            yPosition += 8;
            doc.text(`الرصيد المتبقي: ${formatCurrency(totals.total_balance)}`, 20, yPosition);
            yPosition += 15;

            // جدول الرعاة
            doc.text('تفاصيل الرعاة:', 20, yPosition);
            yPosition += 10;

            // رؤوس الجدول
            doc.text('الراعي', 20, yPosition);
            doc.text('الديون', 70, yPosition);
            doc.text('الحوالات', 110, yPosition);
            doc.text('الرصيد', 150, yPosition);
            yPosition += 8;

            // خط تحت الرؤوس
            doc.line(20, yPosition - 2, 180, yPosition - 2);

            // بيانات الرعاة
            currentReportData.sponsors.forEach(sponsor => {
                if (yPosition > 270) {
                    doc.addPage();
                    yPosition = 20;
                }

                doc.text(sponsor.name.substring(0, 20), 20, yPosition);
                doc.text(formatCurrency(sponsor.total_debts), 70, yPosition);
                doc.text(formatCurrency(sponsor.total_remittances), 110, yPosition);
                doc.text(formatCurrency(sponsor.balance), 150, yPosition);
                yPosition += 8;
            });

        } else if (reportType === 'monthly' && currentReportData) {
            // تصدير التقرير الشهري
            doc.setFontSize(14);
            doc.text(`التقرير الشهري - ${currentReportData.month}/${currentReportData.year}`, 105, yPosition, { align: 'center' });
            yPosition += 15;

            doc.setFontSize(10);
            const totals = currentReportData.totals;

            doc.text(`الديون الشهرية: ${formatCurrency(totals.monthly_debts)}`, 20, yPosition);
            yPosition += 8;
            doc.text(`الحوالات الشهرية: ${formatCurrency(totals.monthly_remittances)}`, 20, yPosition);
            yPosition += 8;
            doc.text(`صافي التغيير: ${formatCurrency(totals.net_change)}`, 20, yPosition);
            yPosition += 15;

            // الديون
            if (currentReportData.debts && currentReportData.debts.length > 0) {
                doc.text('الديون:', 20, yPosition);
                yPosition += 10;

                currentReportData.debts.forEach(debt => {
                    if (yPosition > 270) {
                        doc.addPage();
                        yPosition = 20;
                    }
                    doc.text(`${debt.sponsor_name}: ${formatCurrency(debt.amount)}`, 25, yPosition);
                    yPosition += 6;
                });
                yPosition += 10;
            }

            // الحوالات
            if (currentReportData.remittances && currentReportData.remittances.length > 0) {
                doc.text('الحوالات:', 20, yPosition);
                yPosition += 10;

                currentReportData.remittances.forEach(remittance => {
                    if (yPosition > 270) {
                        doc.addPage();
                        yPosition = 20;
                    }
                    doc.text(`${remittance.sponsor_name}: ${formatCurrency(remittance.amount)}`, 25, yPosition);
                    yPosition += 6;
                });
            }
        }

        // حفظ الملف
        const fileName = `تقرير_${reportType}_${new Date().toISOString().split('T')[0]}.pdf`;
        doc.save(fileName);

        showSuccess('تم تصدير التقرير بنجاح');

    } catch (error) {
        console.error('خطأ في تصدير PDF:', error);
        alert('حدث خطأ أثناء تصدير التقرير. يرجى المحاولة مرة أخرى.');
    }
}

// تحميل التقرير الشهري المحسن
async function loadEnhancedMonthlyReport() {
    const year = document.getElementById('monthlyYear').value || new Date().getFullYear();
    const month = document.getElementById('monthlyMonth').value || (new Date().getMonth() + 1);

    try {
        showLoading('report-content');

        const response = await fetch(`${API_BASE_URL}/reports/monthly?year=${year}&month=${month}`);
        const data = await response.json();

        if (data.success) {
            displayEnhancedMonthlyReport(data.data);
        } else {
            showError('فشل في تحميل التقرير الشهري');
        }
    } catch (error) {
        console.error('خطأ في تحميل التقرير الشهري:', error);
        showError('خطأ في الاتصال بالخادم');
    }
}

// عرض التقرير الشهري المحسن
function displayEnhancedMonthlyReport(data) {
    currentReportData = data;
    currentReportType = 'monthly';

    const monthNames = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];

    let html = `
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-calendar-alt me-2"></i>
                    التقرير الشهري - ${monthNames[data.month - 1]} ${data.year}
                </h5>
                <div class="btn-group">
                    <button class="btn btn-outline-primary btn-sm" onclick="printEnhancedReport()">
                        <i class="fas fa-print me-2"></i>طباعة
                    </button>
                    <button class="btn btn-outline-success btn-sm" onclick="exportToPDF('monthly')">
                        <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- الإحصائيات الشهرية -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="stat-card bg-danger text-white">
                            <div class="stat-icon">
                                <i class="fas fa-minus-circle"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number">${formatCurrency(data.totals.monthly_debts)}</h3>
                                <p class="stat-label">إجمالي الديون الشهرية</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stat-card bg-success text-white">
                            <div class="stat-icon">
                                <i class="fas fa-plus-circle"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number">${formatCurrency(data.totals.monthly_remittances)}</h3>
                                <p class="stat-label">إجمالي الحوالات الشهرية</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="stat-card ${data.totals.net_change >= 0 ? 'bg-warning' : 'bg-info'} text-white">
                            <div class="stat-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="stat-content">
                                <h3 class="stat-number">${formatCurrency(data.totals.net_change)}</h3>
                                <p class="stat-label">صافي التغيير</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الرسم البياني للاتجاهات -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="chart-container">
                            <h6 class="text-center mb-3">اتجاهات المعاملات الشهرية</h6>
                            <div class="chart-wrapper">
                                <canvas id="trendsChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
    `;

    // إضافة جداول الديون والحوالات إذا وجدت
    if (data.debts && data.debts.length > 0) {
        html += `
                <div class="row mb-4">
                    <div class="col-12">
                        <h6 class="mb-3 text-danger">
                            <i class="fas fa-minus-circle me-2"></i>
                            الديون الشهرية (${data.debts.length})
                        </h6>
                        <div class="table-responsive">
                            <table class="table table-striped table-sm">
                                <thead class="table-dark">
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>الراعي</th>
                                        <th>المبلغ</th>
                                        <th>الوصف</th>
                                    </tr>
                                </thead>
                                <tbody>
        `;

        data.debts.forEach(debt => {
            html += `
                <tr>
                    <td>${formatDate(debt.date)}</td>
                    <td>${debt.sponsor_name}</td>
                    <td class="text-danger text-currency">${formatCurrency(debt.amount)}</td>
                    <td>${debt.description || '-'}</td>
                </tr>
            `;
        });

        html += '</tbody></table></div></div></div>';
    }

    if (data.remittances && data.remittances.length > 0) {
        html += `
                <div class="row">
                    <div class="col-12">
                        <h6 class="mb-3 text-success">
                            <i class="fas fa-plus-circle me-2"></i>
                            الحوالات الشهرية (${data.remittances.length})
                        </h6>
                        <div class="table-responsive">
                            <table class="table table-striped table-sm">
                                <thead class="table-dark">
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>الراعي</th>
                                        <th>المبلغ</th>
                                        <th>المصدر</th>
                                    </tr>
                                </thead>
                                <tbody>
        `;

        data.remittances.forEach(remittance => {
            html += `
                <tr>
                    <td>${formatDate(remittance.date)}</td>
                    <td>${remittance.sponsor_name}</td>
                    <td class="text-success text-currency">${formatCurrency(remittance.amount)}</td>
                    <td>${remittance.source || '-'}</td>
                </tr>
            `;
        });

        html += '</tbody></table></div></div></div>';
    }

    html += '</div></div>';

    document.getElementById('report-content').innerHTML = html;

    // إنشاء الرسم البياني للاتجاهات
    setTimeout(() => {
        createTrendsChart(data.debts || [], data.remittances || []);
    }, 100);
}
