// نظام المحاسبة للرعاة - الوظائف الرئيسية

// إعدادات API
const API_BASE_URL = 'http://localhost:5000/api';

// متغيرات عامة
let currentSection = 'dashboard';
let sponsors = [];
let debts = [];
let remittances = [];

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تم تحميل نظام المحاسبة للرعاة');
    
    // Check authentication status
    if (!auth.isAuthenticated()) {
        window.location.href = 'login.html';
        return;
    }
    
    // Update UI based on user role
    updateUIForUserRole();
    
    // Load initial data
    loadDashboard();
    
    // Set up event listeners
    setupEventListeners();
});

// Update UI based on user role
function updateUIForUserRole() {
    const user = auth.getUser();
    const adminMenu = document.getElementById('adminMenu');
    const userNameElement = document.getElementById('userName');
    
    // Update user name in navbar
    if (userNameElement && user) {
        userNameElement.textContent = user.name || user.username;
    }
    
    // Show/hide admin menu based on role
    if (adminMenu) {
        adminMenu.style.display = auth.isAdmin() ? 'block' : 'none';
    }
}

// Set up event listeners
function setupEventListeners() {
    // Add event listeners for modals
    document.querySelectorAll('[data-bs-toggle="modal"]').forEach(button => {
        button.addEventListener('click', function() {
            const target = this.getAttribute('data-bs-target');
            const modal = new bootstrap.Modal(document.querySelector(target));
            modal.show();
        });
    });
    
    // Add logout button event listener
    const logoutButton = document.getElementById('logoutButton');
    if (logoutButton) {
        logoutButton.addEventListener('click', auth.logout);
    }
}

// عرض القسم المحدد
function showSection(sectionName) {
    // إخفاء جميع الأقسام
    const sections = document.querySelectorAll('.content-section');
    sections.forEach(section => {
        section.style.display = 'none';
    });
    
    // إزالة الفئة النشطة من جميع الروابط
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.classList.remove('active');
    });
    
    // عرض القسم المحدد
    const targetSection = document.getElementById(sectionName);
    if (targetSection) {
        targetSection.style.display = 'block';
        currentSection = sectionName;
    }
    
    // إضافة الفئة النشطة للرابط المحدد
    const activeLink = document.querySelector(`[onclick="showSection('${sectionName}')"]`);
    if (activeLink) {
        activeLink.classList.add('active');
    }
    
    // تحميل بيانات القسم
    switch(sectionName) {
        case 'dashboard':
            loadDashboard();
            break;
        case 'sponsors':
            loadSponsors();
            break;
        case 'debts':
            loadDebts();
            break;
        case 'remittances':
            loadRemittances();
            break;
        case 'reports':
            loadReports();
            break;
    }
}

// تحميل لوحة التحكم
async function loadDashboard() {
    try {
        showLoading('recent-transactions');
        
        // تحميل الملخص الإجمالي
        const summaryResponse = await fetch(`${API_BASE_URL}/reports/summary`);
        const summaryData = await summaryResponse.json();
        
        if (summaryData.success) {
            updateDashboardCards(summaryData.data.totals);
            displayRecentTransactions();
        } else {
            showError('فشل في تحميل بيانات الملخص');
        }
    } catch (error) {
        console.error('خطأ في تحميل لوحة التحكم:', error);
        showError('خطأ في الاتصال بالخادم');
    }
}

// تحديث بطاقات الإحصائيات
function updateDashboardCards(totals) {
    document.getElementById('total-sponsors').textContent = totals.sponsors_count || 0;
    document.getElementById('total-debts').textContent = formatCurrency(totals.total_debts || 0);
    document.getElementById('total-remittances').textContent = formatCurrency(totals.total_remittances || 0);
    document.getElementById('total-balance').textContent = formatCurrency(totals.total_balance || 0);
}

// عرض آخر المعاملات
async function displayRecentTransactions() {
    try {
        // الحصول على آخر الديون والحوالات
        const [debtsResponse, remittancesResponse] = await Promise.all([
            fetch(`${API_BASE_URL}/debts`),
            fetch(`${API_BASE_URL}/remittances`)
        ]);
        
        const debtsData = await debtsResponse.json();
        const remittancesData = await remittancesResponse.json();
        
        if (debtsData.success && remittancesData.success) {
            const recentDebts = debtsData.data.slice(0, 5);
            const recentRemittances = remittancesData.data.slice(0, 5);
            
            // دمج وترتيب المعاملات حسب التاريخ
            const allTransactions = [
                ...recentDebts.map(debt => ({...debt, type: 'debt'})),
                ...recentRemittances.map(remittance => ({...remittance, type: 'remittance'}))
            ].sort((a, b) => new Date(b.created_at) - new Date(a.created_at)).slice(0, 10);
            
            displayTransactionsList(allTransactions);
        }
    } catch (error) {
        console.error('خطأ في تحميل المعاملات الأخيرة:', error);
        document.getElementById('recent-transactions').innerHTML = 
            '<p class="text-danger text-center">خطأ في تحميل المعاملات</p>';
    }
}

// عرض قائمة المعاملات
function displayTransactionsList(transactions) {
    const container = document.getElementById('recent-transactions');
    
    if (transactions.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">لا توجد معاملات حديثة</p>';
        return;
    }
    
    let html = '<div class="table-responsive"><table class="table table-striped">';
    html += '<thead><tr><th>النوع</th><th>الراعي</th><th>المبلغ</th><th>التاريخ</th><th>الوصف</th></tr></thead><tbody>';
    
    transactions.forEach(transaction => {
        const typeIcon = transaction.type === 'debt' ? 
            '<i class="fas fa-minus-circle text-danger"></i> دين' : 
            '<i class="fas fa-plus-circle text-success"></i> حوالة';
        
        const amountClass = transaction.type === 'debt' ? 'text-danger' : 'text-success';
        const amountSign = transaction.type === 'debt' ? '-' : '+';
        
        html += `
            <tr>
                <td>${typeIcon}</td>
                <td>${transaction.sponsor_name || 'غير محدد'}</td>
                <td class="${amountClass} text-currency">${amountSign}${formatCurrency(transaction.amount)}</td>
                <td>${formatDate(transaction.date)}</td>
                <td>${transaction.description || transaction.source || '-'}</td>
            </tr>
        `;
    });
    
    html += '</tbody></table></div>';
    container.innerHTML = html;
}

// تحميل الرعاة
async function loadSponsors() {
    try {
        showLoading('sponsors');

        const response = await fetch(`${API_BASE_URL}/sponsors`);
        const data = await response.json();

        if (data.success) {
            displaySponsorsPage(data.data);
        } else {
            showError('فشل في تحميل بيانات الرعاة');
        }
    } catch (error) {
        console.error('خطأ في تحميل الرعاة:', error);
        showError('خطأ في الاتصال بالخادم');
    }
}

// عرض صفحة الرعاة
function displaySponsorsPage(sponsorsData) {
    sponsors = sponsorsData;

    let html = `
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-users me-2"></i>إدارة الرعاة</h2>
            <button class="btn btn-primary" onclick="showAddSponsorModal()">
                <i class="fas fa-plus me-2"></i>إضافة راعي جديد
            </button>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة الرعاة (${sponsors.length})
                </h5>
            </div>
            <div class="card-body">
    `;

    if (sponsors.length === 0) {
        html += '<p class="text-muted text-center">لا توجد رعاة مسجلين</p>';
    } else {
        html += `
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>الهاتف</th>
                            <th>العنوان</th>
                            <th>إجمالي الديون</th>
                            <th>إجمالي الحوالات</th>
                            <th>الرصيد المتبقي</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        sponsors.forEach(sponsor => {
            const balanceClass = sponsor.balance > 0 ? 'text-danger' : sponsor.balance < 0 ? 'text-success' : 'text-muted';

            html += `
                <tr>
                    <td><strong>${sponsor.name}</strong></td>
                    <td>${sponsor.phone || '-'}</td>
                    <td>${sponsor.address || '-'}</td>
                    <td class="text-danger text-currency">${formatCurrency(sponsor.total_debts)}</td>
                    <td class="text-success text-currency">${formatCurrency(sponsor.total_remittances)}</td>
                    <td class="${balanceClass} text-currency"><strong>${formatCurrency(sponsor.balance)}</strong></td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="viewSponsorDetails(${sponsor.id})" title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-warning" onclick="editSponsor(${sponsor.id})" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteSponsor(${sponsor.id}, '${sponsor.name}')" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
    }

    html += `
            </div>
        </div>

        <!-- Modal إضافة/تعديل راعي -->
        <div class="modal fade" id="sponsorModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="sponsorModalTitle">إضافة راعي جديد</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="sponsorForm">
                            <input type="hidden" id="sponsorId">
                            <div class="mb-3">
                                <label for="sponsorName" class="form-label">اسم الراعي *</label>
                                <input type="text" class="form-control" id="sponsorName" required>
                            </div>
                            <div class="mb-3">
                                <label for="sponsorPhone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="sponsorPhone">
                            </div>
                            <div class="mb-3">
                                <label for="sponsorAddress" class="form-label">العنوان</label>
                                <textarea class="form-control" id="sponsorAddress" rows="3"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-primary" onclick="saveSponsor()">حفظ</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('sponsors').innerHTML = html;
}

// تحميل الديون
async function loadDebts() {
    try {
        showLoading('debts');

        const [debtsResponse, sponsorsResponse] = await Promise.all([
            fetch(`${API_BASE_URL}/debts`),
            fetch(`${API_BASE_URL}/sponsors`)
        ]);

        const debtsData = await debtsResponse.json();
        const sponsorsData = await sponsorsResponse.json();

        if (debtsData.success && sponsorsData.success) {
            debts = debtsData.data;
            sponsors = sponsorsData.data;
            displayDebtsPage();
        } else {
            showError('فشل في تحميل بيانات الديون');
        }
    } catch (error) {
        console.error('خطأ في تحميل الديون:', error);
        showError('خطأ في الاتصال بالخادم');
    }
}

// عرض صفحة الديون
function displayDebtsPage() {
    let html = `
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-minus-circle me-2"></i>إدارة الديون</h2>
            <button class="btn btn-danger" onclick="showAddDebtModal()">
                <i class="fas fa-plus me-2"></i>إضافة دين جديد
            </button>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة الديون (${debts.length})
                </h5>
            </div>
            <div class="card-body">
    `;

    if (debts.length === 0) {
        html += '<p class="text-muted text-center">لا توجد ديون مسجلة</p>';
    } else {
        html += `
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>الراعي</th>
                            <th>المبلغ</th>
                            <th>الوصف</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        debts.forEach(debt => {
            html += `
                <tr>
                    <td>${formatDate(debt.date)}</td>
                    <td><strong>${debt.sponsor_name || 'غير محدد'}</strong></td>
                    <td class="text-danger text-currency">${formatCurrency(debt.amount)}</td>
                    <td>${debt.description || '-'}</td>
                    <td>
                        <button class="btn btn-outline-danger btn-sm" onclick="deleteDebt(${debt.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
    }

    html += `
            </div>
        </div>

        <!-- Modal إضافة دين -->
        <div class="modal fade" id="debtModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">إضافة دين جديد</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="debtForm">
                            <div class="mb-3">
                                <label for="debtSponsor" class="form-label">الراعي *</label>
                                <select class="form-select" id="debtSponsor" required>
                                    <option value="">اختر الراعي</option>
    `;

    sponsors.forEach(sponsor => {
        html += `<option value="${sponsor.id}">${sponsor.name}</option>`;
    });

    html += `
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="debtAmount" class="form-label">المبلغ (ريال) *</label>
                                <input type="number" class="form-control" id="debtAmount" min="0" step="1" required>
                            </div>
                            <div class="mb-3">
                                <label for="debtDate" class="form-label">التاريخ *</label>
                                <input type="date" class="form-control" id="debtDate" required>
                            </div>
                            <div class="mb-3">
                                <label for="debtDescription" class="form-label">الوصف</label>
                                <textarea class="form-control" id="debtDescription" rows="3"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-danger" onclick="saveDebt()">حفظ الدين</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('debts').innerHTML = html;

    // تعيين التاريخ الحالي كافتراضي
    document.getElementById('debtDate').value = new Date().toISOString().split('T')[0];
}

// تحميل الحوالات
async function loadRemittances() {
    try {
        showLoading('remittances');

        const [remittancesResponse, sponsorsResponse] = await Promise.all([
            fetch(`${API_BASE_URL}/remittances`),
            fetch(`${API_BASE_URL}/sponsors`)
        ]);

        const remittancesData = await remittancesResponse.json();
        const sponsorsData = await sponsorsResponse.json();

        if (remittancesData.success && sponsorsData.success) {
            remittances = remittancesData.data;
            sponsors = sponsorsData.data;
            displayRemittancesPage();
        } else {
            showError('فشل في تحميل بيانات الحوالات');
        }
    } catch (error) {
        console.error('خطأ في تحميل الحوالات:', error);
        showError('خطأ في الاتصال بالخادم');
    }
}

// عرض صفحة الحوالات
function displayRemittancesPage() {
    let html = `
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-plus-circle me-2"></i>إدارة الحوالات</h2>
            <button class="btn btn-success" onclick="showAddRemittanceModal()">
                <i class="fas fa-plus me-2"></i>إضافة حوالة جديدة
            </button>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    قائمة الحوالات (${remittances.length})
                </h5>
            </div>
            <div class="card-body">
    `;

    if (remittances.length === 0) {
        html += '<p class="text-muted text-center">لا توجد حوالات مسجلة</p>';
    } else {
        html += `
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>الراعي</th>
                            <th>المبلغ</th>
                            <th>مصدر الحوالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        remittances.forEach(remittance => {
            html += `
                <tr>
                    <td>${formatDate(remittance.date)}</td>
                    <td><strong>${remittance.sponsor_name || 'غير محدد'}</strong></td>
                    <td class="text-success text-currency">${formatCurrency(remittance.amount)}</td>
                    <td>${remittance.source || '-'}</td>
                    <td>
                        <button class="btn btn-outline-danger btn-sm" onclick="deleteRemittance(${remittance.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
    }

    html += `
            </div>
        </div>

        <!-- Modal إضافة حوالة -->
        <div class="modal fade" id="remittanceModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">إضافة حوالة جديدة</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="remittanceForm">
                            <div class="mb-3">
                                <label for="remittanceSponsor" class="form-label">الراعي *</label>
                                <select class="form-select" id="remittanceSponsor" required>
                                    <option value="">اختر الراعي</option>
    `;

    sponsors.forEach(sponsor => {
        html += `<option value="${sponsor.id}">${sponsor.name}</option>`;
    });

    html += `
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="remittanceAmount" class="form-label">المبلغ (ريال) *</label>
                                <input type="number" class="form-control" id="remittanceAmount" min="0" step="1" required>
                            </div>
                            <div class="mb-3">
                                <label for="remittanceDate" class="form-label">التاريخ *</label>
                                <input type="date" class="form-control" id="remittanceDate" required>
                            </div>
                            <div class="mb-3">
                                <label for="remittanceSource" class="form-label">مصدر الحوالة</label>
                                <input type="text" class="form-control" id="remittanceSource" placeholder="مثال: بنك الكريمي، حوالة نقدية">
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-success" onclick="saveRemittance()">حفظ الحوالة</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('remittances').innerHTML = html;

    // تعيين التاريخ الحالي كافتراضي
    document.getElementById('remittanceDate').value = new Date().toISOString().split('T')[0];
}

// تحميل التقارير
async function loadReports() {
    try {
        showLoading('reports');
        displayReportsPage();
    } catch (error) {
        console.error('خطأ في تحميل التقارير:', error);
        showError('خطأ في تحميل التقارير');
    }
}

// عرض صفحة التقارير
function displayReportsPage() {
    let html = `
        <div class="row">
            <div class="col-12">
                <h2 class="mb-4">
                    <i class="fas fa-chart-bar me-2"></i>
                    التقارير
                </h2>
            </div>
        </div>

        <!-- أزرار التقارير المحسنة -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card h-100 report-card">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-pie fa-3x text-primary mb-3"></i>
                        <h5>الملخص الإجمالي المحسن</h5>
                        <p class="text-muted">ملخص شامل مع رسوم بيانية وإحصائيات متقدمة</p>
                        <button class="btn btn-primary" onclick="loadSummaryReport()">
                            <i class="fas fa-chart-line me-2"></i>عرض التقرير
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card h-100 report-card">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar-day fa-3x text-success mb-3"></i>
                        <h5>التقرير اليومي</h5>
                        <p class="text-muted">معاملات تاريخ محدد مع تحليل مفصل</p>
                        <button class="btn btn-success" onclick="showDailyReportModal()">
                            <i class="fas fa-eye me-2"></i>عرض التقرير
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card h-100 report-card">
                    <div class="card-body text-center">
                        <i class="fas fa-calendar-alt fa-3x text-info mb-3"></i>
                        <h5>التقرير الشهري</h5>
                        <p class="text-muted">تحليل شامل للمعاملات الشهرية مع الاتجاهات</p>
                        <button class="btn btn-info" onclick="showMonthlyReportModal()">
                            <i class="fas fa-chart-bar me-2"></i>عرض التقرير
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card h-100 report-card">
                    <div class="card-body text-center">
                        <i class="fas fa-user fa-3x text-warning mb-3"></i>
                        <h5>تقرير الراعي</h5>
                        <p class="text-muted">تفاصيل شاملة لراعي محدد مع التاريخ</p>
                        <button class="btn btn-warning" onclick="showSponsorReportModal()">
                            <i class="fas fa-user-chart me-2"></i>عرض التقرير
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- التقارير الجديدة للفترات الزمنية -->
        <div class="row mb-4">
            <div class="col-12">
                <h4 class="text-primary mb-3">
                    <i class="fas fa-calendar-range me-2"></i>
                    تقارير الفترات الزمنية
                </h4>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-lg-4 col-md-6 mb-3">
                <div class="card h-100 report-card border-success">
                    <div class="card-body text-center">
                        <i class="fas fa-money-bill-transfer fa-3x text-success mb-3"></i>
                        <h5>تقرير الحوالات للفترة</h5>
                        <p class="text-muted">جميع الحوالات المستلمة خلال فترة محددة مع التجميع حسب الراعي والمصدر</p>
                        <button class="btn btn-success" onclick="showRemittancesPeriodModal()">
                            <i class="fas fa-calendar-plus me-2"></i>عرض التقرير
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-3">
                <div class="card h-100 report-card border-danger">
                    <div class="card-body text-center">
                        <i class="fas fa-file-invoice-dollar fa-3x text-danger mb-3"></i>
                        <h5>تقرير الديون للفترة</h5>
                        <p class="text-muted">جميع الديون المسجلة خلال فترة محددة مع التجميع حسب الراعي</p>
                        <button class="btn btn-danger" onclick="showDebtsPeriodModal()">
                            <i class="fas fa-calendar-minus me-2"></i>عرض التقرير
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-3">
                <div class="card h-100 report-card border-primary">
                    <div class="card-body text-center">
                        <i class="fas fa-balance-scale fa-3x text-primary mb-3"></i>
                        <h5>تقرير الأرصدة للفترة</h5>
                        <p class="text-muted">الرصيد المتبقي لكل راعي خلال فترة محددة مع التصنيف</p>
                        <button class="btn btn-primary" onclick="showBalancePeriodModal()">
                            <i class="fas fa-calculator me-2"></i>عرض التقرير
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- منطقة عرض التقارير -->
        <div id="report-content">
            <div class="card">
                <div class="card-body text-center text-muted">
                    <i class="fas fa-chart-bar fa-4x mb-3"></i>
                    <h5>اختر نوع التقرير</h5>
                    <p>استخدم الأزرار أعلاه لعرض التقارير المختلفة</p>
                </div>
            </div>
        </div>

        <!-- Modal التقرير اليومي -->
        <div class="modal fade" id="dailyReportModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">التقرير اليومي</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="reportDate" class="form-label">اختر التاريخ</label>
                            <input type="date" class="form-control" id="reportDate">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-success" onclick="loadDailyReport()">عرض التقرير</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal التقرير الشهري -->
        <div class="modal fade" id="monthlyReportModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">التقرير الشهري</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="monthlyYear" class="form-label">السنة</label>
                                <select class="form-select" id="monthlyYear">
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="monthlyMonth" class="form-label">الشهر</label>
                                <select class="form-select" id="monthlyMonth">
                                    <option value="1">يناير</option>
                                    <option value="2">فبراير</option>
                                    <option value="3">مارس</option>
                                    <option value="4">أبريل</option>
                                    <option value="5">مايو</option>
                                    <option value="6">يونيو</option>
                                    <option value="7">يوليو</option>
                                    <option value="8">أغسطس</option>
                                    <option value="9">سبتمبر</option>
                                    <option value="10">أكتوبر</option>
                                    <option value="11">نوفمبر</option>
                                    <option value="12">ديسمبر</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-info" onclick="loadEnhancedMonthlyReport()">عرض التقرير</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal تقرير الراعي -->
        <div class="modal fade" id="sponsorReportModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">تقرير الراعي</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="reportSponsor" class="form-label">اختر الراعي</label>
                            <select class="form-select" id="reportSponsor">
                                <option value="">جاري تحميل الرعاة...</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-warning" onclick="loadSponsorReport()">عرض التقرير</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal تقرير الحوالات للفترة -->
        <div class="modal fade" id="remittancesPeriodModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">تقرير الحوالات للفترة المحددة</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="remittancesFromDate" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="remittancesFromDate" required>
                            </div>
                            <div class="col-md-6">
                                <label for="remittancesToDate" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="remittancesToDate" required>
                            </div>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                سيتم عرض جميع الحوالات المستلمة خلال الفترة المحددة مع التجميع حسب الراعي ومصدر الحوالة
                            </small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-success" onclick="loadRemittancesPeriodReport()">عرض التقرير</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal تقرير الديون للفترة -->
        <div class="modal fade" id="debtsPeriodModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">تقرير الديون للفترة المحددة</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="debtsFromDate" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="debtsFromDate" required>
                            </div>
                            <div class="col-md-6">
                                <label for="debtsToDate" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="debtsToDate" required>
                            </div>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                سيتم عرض جميع الديون المسجلة خلال الفترة المحددة مع التجميع حسب الراعي
                            </small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-danger" onclick="loadDebtsPeriodReport()">عرض التقرير</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal تقرير الأرصدة للفترة -->
        <div class="modal fade" id="balancePeriodModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">تقرير الأرصدة للفترة المحددة</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="balanceFromDate" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="balanceFromDate" required>
                            </div>
                            <div class="col-md-6">
                                <label for="balanceToDate" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="balanceToDate" required>
                            </div>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                سيتم حساب الرصيد المتبقي لكل راعي خلال الفترة المحددة وتصنيف الرعاة (مدين/دائن/متوازن)
                            </small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-primary" onclick="loadBalancePeriodReport()">عرض التقرير</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('reports').innerHTML = html;

    // تعيين التاريخ الحالي كافتراضي
    document.getElementById('reportDate').value = new Date().toISOString().split('T')[0];

    // تحميل قائمة الرعاة للتقرير
    loadSponsorsForReport();
}

// وظائف مساعدة

// تنسيق العملة
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount) + ' ريال';
}

// تنسيق التاريخ
function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-YE', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        numberingSystem: 'latn'  // استخدام الأرقام اللاتينية
    });
}

// عرض رسالة التحميل
function showLoading(containerId) {
    const container = document.getElementById(containerId);
    if (container) {
        container.innerHTML = `
            <div class="loading">
                <i class="fas fa-spinner fa-spin fa-2x"></i>
                <p class="mt-2">جاري التحميل...</p>
            </div>
        `;
    }
}

// عرض رسالة التحميل المحسنة مع شريط التقدم
function showEnhancedLoading(containerId, message = 'جاري تحميل التقرير المحسن') {
    const container = document.getElementById(containerId);
    if (container) {
        container.innerHTML = `
            <div class="loading-enhanced">
                <i class="fas fa-chart-line fa-spin"></i>
                <h5>${message}</h5>
                <p>يرجى الانتظار بينما نقوم بإعداد البيانات والرسوم البيانية...</p>
                <div class="progress mt-3" style="height: 6px;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                         role="progressbar" style="width: 0%" id="loadingProgress"></div>
                </div>
            </div>
        `;

        // محاكاة شريط التقدم
        simulateProgress();
    }
}

// محاكاة تقدم التحميل
function simulateProgress() {
    const progressBar = document.getElementById('loadingProgress');
    if (!progressBar) return;

    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 90) progress = 90;

        progressBar.style.width = progress + '%';

        if (progress >= 90) {
            clearInterval(interval);
        }
    }, 200);

    // إكمال التقدم عند انتهاء التحميل
    setTimeout(() => {
        if (progressBar) {
            progressBar.style.width = '100%';
            setTimeout(() => {
                clearInterval(interval);
            }, 300);
        }
    }, 2000);
}

// عرض رسالة خطأ
function showError(message) {
    console.error('خطأ:', message);
    // يمكن إضافة نظام إشعارات هنا
}

// عرض رسالة نجاح
function showSuccess(message) {
    console.log('نجح:', message);
    // يمكن إضافة نظام إشعارات هنا
}

// ===== وظائف إدارة الرعاة =====

// عرض نموذج إضافة راعي جديد
function showAddSponsorModal() {
    document.getElementById('sponsorModalTitle').textContent = 'إضافة راعي جديد';
    document.getElementById('sponsorForm').reset();
    document.getElementById('sponsorId').value = '';

    const modal = new bootstrap.Modal(document.getElementById('sponsorModal'));
    modal.show();
}

// تعديل راعي
function editSponsor(sponsorId) {
    const sponsor = sponsors.find(s => s.id === sponsorId);
    if (!sponsor) return;

    document.getElementById('sponsorModalTitle').textContent = 'تعديل بيانات الراعي';
    document.getElementById('sponsorId').value = sponsor.id;
    document.getElementById('sponsorName').value = sponsor.name;
    document.getElementById('sponsorPhone').value = sponsor.phone || '';
    document.getElementById('sponsorAddress').value = sponsor.address || '';

    const modal = new bootstrap.Modal(document.getElementById('sponsorModal'));
    modal.show();
}

// حفظ راعي (إضافة أو تعديل)
async function saveSponsor() {
    const sponsorId = document.getElementById('sponsorId').value;
    const name = document.getElementById('sponsorName').value.trim();
    const phone = document.getElementById('sponsorPhone').value.trim();
    const address = document.getElementById('sponsorAddress').value.trim();

    if (!name) {
        alert('اسم الراعي مطلوب');
        return;
    }

    const sponsorData = { name, phone, address };

    try {
        let response;
        if (sponsorId) {
            // تعديل راعي موجود
            response = await fetch(`${API_BASE_URL}/sponsors/${sponsorId}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(sponsorData)
            });
        } else {
            // إضافة راعي جديد
            response = await fetch(`${API_BASE_URL}/sponsors`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(sponsorData)
            });
        }

        const data = await response.json();

        if (data.success) {
            showSuccess(data.message);
            bootstrap.Modal.getInstance(document.getElementById('sponsorModal')).hide();
            loadSponsors(); // إعادة تحميل قائمة الرعاة
        } else {
            alert('خطأ: ' + data.error);
        }
    } catch (error) {
        console.error('خطأ في حفظ الراعي:', error);
        alert('خطأ في الاتصال بالخادم');
    }
}

// حذف راعي
async function deleteSponsor(sponsorId, sponsorName) {
    if (!confirm(`هل أنت متأكد من حذف الراعي "${sponsorName}"؟\nسيتم حذف جميع الديون والحوالات المرتبطة به.`)) {
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}/sponsors/${sponsorId}`, {
            method: 'DELETE'
        });

        const data = await response.json();

        if (data.success) {
            showSuccess(data.message);
            loadSponsors(); // إعادة تحميل قائمة الرعاة
        } else {
            alert('خطأ: ' + data.error);
        }
    } catch (error) {
        console.error('خطأ في حذف الراعي:', error);
        alert('خطأ في الاتصال بالخادم');
    }
}

// عرض تفاصيل راعي
function viewSponsorDetails(sponsorId) {
    const sponsor = sponsors.find(s => s.id === sponsorId);
    if (!sponsor) return;

    // الانتقال إلى صفحة التقارير مع عرض تقرير الراعي
    showSection('reports');
    // سيتم تطوير هذه الوظيفة لاحقاً
}

// ===== وظائف إدارة الديون =====

// عرض نموذج إضافة دين جديد
function showAddDebtModal() {
    document.getElementById('debtForm').reset();
    document.getElementById('debtDate').value = new Date().toISOString().split('T')[0];

    const modal = new bootstrap.Modal(document.getElementById('debtModal'));
    modal.show();
}

// حفظ دين جديد
async function saveDebt() {
    const sponsorId = document.getElementById('debtSponsor').value;
    const amount = document.getElementById('debtAmount').value;
    const date = document.getElementById('debtDate').value;
    const description = document.getElementById('debtDescription').value.trim();

    if (!sponsorId || !amount || !date) {
        alert('جميع الحقول المطلوبة يجب ملؤها');
        return;
    }

    if (parseFloat(amount) <= 0) {
        alert('المبلغ يجب أن يكون أكبر من صفر');
        return;
    }

    const debtData = {
        sponsor_id: parseInt(sponsorId),
        amount: parseFloat(amount),
        date: date,
        description: description
    };

    try {
        const response = await fetch(`${API_BASE_URL}/debts`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(debtData)
        });

        const data = await response.json();

        if (data.success) {
            showSuccess(data.message);
            bootstrap.Modal.getInstance(document.getElementById('debtModal')).hide();
            loadDebts(); // إعادة تحميل قائمة الديون
        } else {
            alert('خطأ: ' + data.error);
        }
    } catch (error) {
        console.error('خطأ في حفظ الدين:', error);
        alert('خطأ في الاتصال بالخادم');
    }
}

// حذف دين
async function deleteDebt(debtId) {
    if (!confirm('هل أنت متأكد من حذف هذا الدين؟')) {
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}/debts/${debtId}`, {
            method: 'DELETE'
        });

        const data = await response.json();

        if (data.success) {
            showSuccess(data.message);
            loadDebts(); // إعادة تحميل قائمة الديون
        } else {
            alert('خطأ: ' + data.error);
        }
    } catch (error) {
        console.error('خطأ في حذف الدين:', error);
        alert('خطأ في الاتصال بالخادم');
    }
}

// ===== وظائف إدارة الحوالات =====

// عرض نموذج إضافة حوالة جديدة
function showAddRemittanceModal() {
    document.getElementById('remittanceForm').reset();
    document.getElementById('remittanceDate').value = new Date().toISOString().split('T')[0];

    const modal = new bootstrap.Modal(document.getElementById('remittanceModal'));
    modal.show();
}

// حفظ حوالة جديدة
async function saveRemittance() {
    const sponsorId = document.getElementById('remittanceSponsor').value;
    const amount = document.getElementById('remittanceAmount').value;
    const date = document.getElementById('remittanceDate').value;
    const source = document.getElementById('remittanceSource').value.trim();

    if (!sponsorId || !amount || !date) {
        alert('جميع الحقول المطلوبة يجب ملؤها');
        return;
    }

    if (parseFloat(amount) <= 0) {
        alert('المبلغ يجب أن يكون أكبر من صفر');
        return;
    }

    const remittanceData = {
        sponsor_id: parseInt(sponsorId),
        amount: parseFloat(amount),
        date: date,
        source: source
    };

    try {
        const response = await fetch(`${API_BASE_URL}/remittances`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(remittanceData)
        });

        const data = await response.json();

        if (data.success) {
            showSuccess(data.message);
            bootstrap.Modal.getInstance(document.getElementById('remittanceModal')).hide();
            loadRemittances(); // إعادة تحميل قائمة الحوالات
        } else {
            alert('خطأ: ' + data.error);
        }
    } catch (error) {
        console.error('خطأ في حفظ الحوالة:', error);
        alert('خطأ في الاتصال بالخادم');
    }
}

// حذف حوالة
async function deleteRemittance(remittanceId) {
    if (!confirm('هل أنت متأكد من حذف هذه الحوالة؟')) {
        return;
    }

    try {
        const response = await fetch(`${API_BASE_URL}/remittances/${remittanceId}`, {
            method: 'DELETE'
        });

        const data = await response.json();

        if (data.success) {
            showSuccess(data.message);
            loadRemittances(); // إعادة تحميل قائمة الحوالات
        } else {
            alert('خطأ: ' + data.error);
        }
    } catch (error) {
        console.error('خطأ في حذف الحوالة:', error);
        alert('خطأ في الاتصال بالخادم');
    }
}

// ===== وظائف التقارير =====

// تحميل قائمة الرعاة للتقارير
async function loadSponsorsForReport() {
    try {
        const response = await fetch(`${API_BASE_URL}/sponsors`);
        const data = await response.json();

        if (data.success) {
            const select = document.getElementById('reportSponsor');
            select.innerHTML = '<option value="">اختر الراعي</option>';

            data.data.forEach(sponsor => {
                select.innerHTML += `<option value="${sponsor.id}">${sponsor.name}</option>`;
            });
        }
    } catch (error) {
        console.error('خطأ في تحميل الرعاة:', error);
    }
}

// عرض نموذج التقرير اليومي
function showDailyReportModal() {
    document.getElementById('reportDate').value = new Date().toISOString().split('T')[0];
    const modal = new bootstrap.Modal(document.getElementById('dailyReportModal'));
    modal.show();
}

// عرض نموذج التقرير الشهري
function showMonthlyReportModal() {
    // تعبئة قائمة السنوات
    const currentYear = new Date().getFullYear();
    const currentMonth = new Date().getMonth() + 1;

    let yearOptions = '';
    for (let year = currentYear - 5; year <= currentYear + 1; year++) {
        yearOptions += `<option value="${year}" ${year === currentYear ? 'selected' : ''}>${year}</option>`;
    }
    document.getElementById('monthlyYear').innerHTML = yearOptions;

    // تحديد الشهر الحالي
    document.getElementById('monthlyMonth').value = currentMonth;

    const modal = new bootstrap.Modal(document.getElementById('monthlyReportModal'));
    modal.show();
}

// عرض نموذج تقرير الراعي
function showSponsorReportModal() {
    const modal = new bootstrap.Modal(document.getElementById('sponsorReportModal'));
    modal.show();
}

// ===== وظائف التقارير الجديدة للفترات الزمنية =====

// عرض نموذج تقرير الحوالات للفترة
function showRemittancesPeriodModal() {
    // تعيين التواريخ الافتراضية (آخر 30 يوم)
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));

    document.getElementById('remittancesFromDate').value = thirtyDaysAgo.toISOString().split('T')[0];
    document.getElementById('remittancesToDate').value = today.toISOString().split('T')[0];

    const modal = new bootstrap.Modal(document.getElementById('remittancesPeriodModal'));
    modal.show();
}

// عرض نموذج تقرير الديون للفترة
function showDebtsPeriodModal() {
    // تعيين التواريخ الافتراضية (آخر 30 يوم)
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));

    document.getElementById('debtsFromDate').value = thirtyDaysAgo.toISOString().split('T')[0];
    document.getElementById('debtsToDate').value = today.toISOString().split('T')[0];

    const modal = new bootstrap.Modal(document.getElementById('debtsPeriodModal'));
    modal.show();
}

// عرض نموذج تقرير الأرصدة للفترة
function showBalancePeriodModal() {
    // تعيين التواريخ الافتراضية (آخر 30 يوم)
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));

    document.getElementById('balanceFromDate').value = thirtyDaysAgo.toISOString().split('T')[0];
    document.getElementById('balanceToDate').value = today.toISOString().split('T')[0];

    const modal = new bootstrap.Modal(document.getElementById('balancePeriodModal'));
    modal.show();
}

// تحميل تقرير الحوالات للفترة
async function loadRemittancesPeriodReport() {
    const fromDate = document.getElementById('remittancesFromDate').value;
    const toDate = document.getElementById('remittancesToDate').value;

    if (!fromDate || !toDate) {
        alert('يرجى تحديد تاريخ البداية والنهاية');
        return;
    }

    if (new Date(fromDate) > new Date(toDate)) {
        alert('تاريخ البداية يجب أن يكون قبل تاريخ النهاية');
        return;
    }

    try {
        bootstrap.Modal.getInstance(document.getElementById('remittancesPeriodModal')).hide();
        showEnhancedLoading('report-content', 'جاري تحميل تقرير الحوالات للفترة المحددة');

        const response = await fetch(`${API_BASE_URL}/reports/remittances-period?from_date=${fromDate}&to_date=${toDate}`);
        const data = await response.json();

        if (data.success) {
            displayRemittancesPeriodReport(data.data);
        } else {
            showError('فشل في تحميل تقرير الحوالات: ' + data.error);
        }
    } catch (error) {
        console.error('خطأ في تحميل تقرير الحوالات:', error);
        showError('خطأ في الاتصال بالخادم');
    }
}

// تحميل تقرير الديون للفترة
async function loadDebtsPeriodReport() {
    const fromDate = document.getElementById('debtsFromDate').value;
    const toDate = document.getElementById('debtsToDate').value;

    if (!fromDate || !toDate) {
        alert('يرجى تحديد تاريخ البداية والنهاية');
        return;
    }

    if (new Date(fromDate) > new Date(toDate)) {
        alert('تاريخ البداية يجب أن يكون قبل تاريخ النهاية');
        return;
    }

    try {
        bootstrap.Modal.getInstance(document.getElementById('debtsPeriodModal')).hide();
        showEnhancedLoading('report-content', 'جاري تحميل تقرير الديون للفترة المحددة');

        const response = await fetch(`${API_BASE_URL}/reports/debts-period?from_date=${fromDate}&to_date=${toDate}`);
        const data = await response.json();

        if (data.success) {
            displayDebtsPeriodReport(data.data);
        } else {
            showError('فشل في تحميل تقرير الديون: ' + data.error);
        }
    } catch (error) {
        console.error('خطأ في تحميل تقرير الديون:', error);
        showError('خطأ في الاتصال بالخادم');
    }
}

// تحميل تقرير الأرصدة للفترة
async function loadBalancePeriodReport() {
    const fromDate = document.getElementById('balanceFromDate').value;
    const toDate = document.getElementById('balanceToDate').value;

    if (!fromDate || !toDate) {
        alert('يرجى تحديد تاريخ البداية والنهاية');
        return;
    }

    if (new Date(fromDate) > new Date(toDate)) {
        alert('تاريخ البداية يجب أن يكون قبل تاريخ النهاية');
        return;
    }

    try {
        bootstrap.Modal.getInstance(document.getElementById('balancePeriodModal')).hide();
        showEnhancedLoading('report-content', 'جاري تحميل تقرير الأرصدة للفترة المحددة');

        const response = await fetch(`${API_BASE_URL}/reports/balance-period?from_date=${fromDate}&to_date=${toDate}`);
        const data = await response.json();

        if (data.success) {
            displayBalancePeriodReport(data.data);
        } else {
            showError('فشل في تحميل تقرير الأرصدة: ' + data.error);
        }
    } catch (error) {
        console.error('خطأ في تحميل تقرير الأرصدة:', error);
        showError('خطأ في الاتصال بالخادم');
    }
}

// تحميل الملخص الإجمالي المحسن مع تحسينات الأداء
async function loadSummaryReport() {
    try {
        // بدء مراقبة الأداء
        if (window.performanceOptimizations) {
            window.performanceOptimizations.performanceMonitor.startTimer('summary-report-load');
        }

        showEnhancedLoading('report-content', 'جاري تحميل الملخص الإجمالي المحسن');

        // استخدام الطلب المحسن مع التخزين المؤقت
        const data = window.performanceOptimizations ?
            await window.performanceOptimizations.optimizedFetch(`${API_BASE_URL}/reports/summary`) :
            await fetch(`${API_BASE_URL}/reports/summary`).then(r => r.json());

        if (data.success) {
            displayEnhancedSummaryReport(data.data);

            // انتهاء مراقبة الأداء
            if (window.performanceOptimizations) {
                window.performanceOptimizations.performanceMonitor.endTimer('summary-report-load');
            }
        } else {
            showError('فشل في تحميل التقرير');
        }
    } catch (error) {
        console.error('خطأ في تحميل الملخص الإجمالي:', error);
        showError('خطأ في الاتصال بالخادم');
    }
}

// عرض الملخص الإجمالي
function displaySummaryReport(data) {
    let html = `
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    الملخص الإجمالي
                </h5>
                <button class="btn btn-outline-primary btn-sm" onclick="printReport()">
                    <i class="fas fa-print me-2"></i>طباعة
                </button>
            </div>
            <div class="card-body">
                <!-- الإحصائيات العامة -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h3 class="text-primary">${data.totals.sponsors_count}</h3>
                            <p class="text-muted">إجمالي الرعاة</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h3 class="text-danger">${formatCurrency(data.totals.total_debts)}</h3>
                            <p class="text-muted">إجمالي الديون</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h3 class="text-success">${formatCurrency(data.totals.total_remittances)}</h3>
                            <p class="text-muted">إجمالي الحوالات</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h3 class="text-warning">${formatCurrency(data.totals.total_balance)}</h3>
                            <p class="text-muted">الرصيد المتبقي</p>
                        </div>
                    </div>
                </div>

                <!-- تفاصيل الرعاة -->
                <h6 class="mb-3">تفاصيل الرعاة:</h6>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>الراعي</th>
                                <th>إجمالي الديون</th>
                                <th>إجمالي الحوالات</th>
                                <th>الرصيد المتبقي</th>
                            </tr>
                        </thead>
                        <tbody>
    `;

    data.sponsors.forEach(sponsor => {
        const balanceClass = sponsor.balance > 0 ? 'text-danger' : sponsor.balance < 0 ? 'text-success' : 'text-muted';
        html += `
            <tr>
                <td><strong>${sponsor.name}</strong></td>
                <td class="text-danger text-currency">${formatCurrency(sponsor.total_debts)}</td>
                <td class="text-success text-currency">${formatCurrency(sponsor.total_remittances)}</td>
                <td class="${balanceClass} text-currency"><strong>${formatCurrency(sponsor.balance)}</strong></td>
            </tr>
        `;
    });

    html += `
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    `;

    document.getElementById('report-content').innerHTML = html;
}

// تحميل التقرير اليومي
async function loadDailyReport() {
    const date = document.getElementById('reportDate').value;
    if (!date) {
        alert('يرجى اختيار التاريخ');
        return;
    }

    try {
        bootstrap.Modal.getInstance(document.getElementById('dailyReportModal')).hide();
        showLoading('report-content');

        const response = await fetch(`${API_BASE_URL}/reports/daily?date=${date}`);
        const data = await response.json();

        if (data.success) {
            displayDailyReport(data.data);
        } else {
            showError('فشل في تحميل التقرير اليومي');
        }
    } catch (error) {
        console.error('خطأ في تحميل التقرير اليومي:', error);
        showError('خطأ في الاتصال بالخادم');
    }
}

// عرض التقرير اليومي
function displayDailyReport(data) {
    let html = `
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-calendar-day me-2"></i>
                    التقرير اليومي - ${formatDate(data.date)}
                </h5>
                <button class="btn btn-outline-primary btn-sm" onclick="printReport()">
                    <i class="fas fa-print me-2"></i>طباعة
                </button>
            </div>
            <div class="card-body">
                <!-- الإحصائيات اليومية -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="text-center">
                            <h4 class="text-danger">${formatCurrency(data.totals.daily_debts)}</h4>
                            <p class="text-muted">إجمالي الديون اليومية</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h4 class="text-success">${formatCurrency(data.totals.daily_remittances)}</h4>
                            <p class="text-muted">إجمالي الحوالات اليومية</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h4 class="text-warning">${formatCurrency(data.totals.net_change)}</h4>
                            <p class="text-muted">صافي التغيير</p>
                        </div>
                    </div>
                </div>
    `;

    // عرض الديون اليومية
    if (data.debts.length > 0) {
        html += `
                <h6 class="mb-3 text-danger">
                    <i class="fas fa-minus-circle me-2"></i>
                    الديون (${data.debts.length})
                </h6>
                <div class="table-responsive mb-4">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>الراعي</th>
                                <th>المبلغ</th>
                                <th>الوصف</th>
                            </tr>
                        </thead>
                        <tbody>
        `;

        data.debts.forEach(debt => {
            html += `
                <tr>
                    <td>${debt.sponsor_name}</td>
                    <td class="text-danger text-currency">${formatCurrency(debt.amount)}</td>
                    <td>${debt.description || '-'}</td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
    }

    // عرض الحوالات اليومية
    if (data.remittances.length > 0) {
        html += `
                <h6 class="mb-3 text-success">
                    <i class="fas fa-plus-circle me-2"></i>
                    الحوالات (${data.remittances.length})
                </h6>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>الراعي</th>
                                <th>المبلغ</th>
                                <th>المصدر</th>
                            </tr>
                        </thead>
                        <tbody>
        `;

        data.remittances.forEach(remittance => {
            html += `
                <tr>
                    <td>${remittance.sponsor_name}</td>
                    <td class="text-success text-currency">${formatCurrency(remittance.amount)}</td>
                    <td>${remittance.source || '-'}</td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
    }

    if (data.debts.length === 0 && data.remittances.length === 0) {
        html += '<p class="text-muted text-center">لا توجد معاملات في هذا التاريخ</p>';
    }

    html += '</div></div>';

    document.getElementById('report-content').innerHTML = html;
}

// تحميل تقرير الراعي
async function loadSponsorReport() {
    const sponsorId = document.getElementById('reportSponsor').value;
    if (!sponsorId) {
        alert('يرجى اختيار الراعي');
        return;
    }

    try {
        bootstrap.Modal.getInstance(document.getElementById('sponsorReportModal')).hide();
        showLoading('report-content');

        const response = await fetch(`${API_BASE_URL}/reports/sponsor/${sponsorId}`);
        const data = await response.json();

        if (data.success) {
            displaySponsorReport(data.data);
        } else {
            showError('فشل في تحميل تقرير الراعي');
        }
    } catch (error) {
        console.error('خطأ في تحميل تقرير الراعي:', error);
        showError('خطأ في الاتصال بالخادم');
    }
}

// عرض تقرير الراعي
function displaySponsorReport(data) {
    const sponsor = data.sponsor;
    const balanceClass = sponsor.balance > 0 ? 'text-danger' : sponsor.balance < 0 ? 'text-success' : 'text-muted';

    let html = `
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    تقرير الراعي - ${sponsor.name}
                </h5>
                <button class="btn btn-outline-primary btn-sm" onclick="printReport()">
                    <i class="fas fa-print me-2"></i>طباعة
                </button>
            </div>
            <div class="card-body">
                <!-- معلومات الراعي -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6>معلومات الراعي:</h6>
                        <p><strong>الاسم:</strong> ${sponsor.name}</p>
                        <p><strong>الهاتف:</strong> ${sponsor.phone || 'غير محدد'}</p>
                        <p><strong>العنوان:</strong> ${sponsor.address || 'غير محدد'}</p>
                    </div>
                    <div class="col-md-6">
                        <h6>الملخص المالي:</h6>
                        <p><strong class="text-danger">إجمالي الديون:</strong> ${formatCurrency(sponsor.total_debts)}</p>
                        <p><strong class="text-success">إجمالي الحوالات:</strong> ${formatCurrency(sponsor.total_remittances)}</p>
                        <p><strong class="${balanceClass}">الرصيد المتبقي:</strong> ${formatCurrency(sponsor.balance)}</p>
                    </div>
                </div>
    `;

    // عرض الديون
    if (data.debts.length > 0) {
        html += `
                <h6 class="mb-3 text-danger">
                    <i class="fas fa-minus-circle me-2"></i>
                    الديون (${data.debts.length})
                </h6>
                <div class="table-responsive mb-4">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>المبلغ</th>
                                <th>الوصف</th>
                            </tr>
                        </thead>
                        <tbody>
        `;

        data.debts.forEach(debt => {
            html += `
                <tr>
                    <td>${formatDate(debt.date)}</td>
                    <td class="text-danger text-currency">${formatCurrency(debt.amount)}</td>
                    <td>${debt.description || '-'}</td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
    }

    // عرض الحوالات
    if (data.remittances.length > 0) {
        html += `
                <h6 class="mb-3 text-success">
                    <i class="fas fa-plus-circle me-2"></i>
                    الحوالات (${data.remittances.length})
                </h6>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>المبلغ</th>
                                <th>المصدر</th>
                            </tr>
                        </thead>
                        <tbody>
        `;

        data.remittances.forEach(remittance => {
            html += `
                <tr>
                    <td>${formatDate(remittance.date)}</td>
                    <td class="text-success text-currency">${formatCurrency(remittance.amount)}</td>
                    <td>${remittance.source || '-'}</td>
                </tr>
            `;
        });

        html += '</tbody></table></div>';
    }

    if (data.debts.length === 0 && data.remittances.length === 0) {
        html += '<p class="text-muted text-center">لا توجد معاملات لهذا الراعي</p>';
    }

    html += '</div></div>';

    document.getElementById('report-content').innerHTML = html;
}

// طباعة التقرير
function printReport() {
    const reportContent = document.getElementById('report-content').innerHTML;
    const printWindow = window.open('', '_blank');

    printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>تقرير نظام المحاسبة</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
            <link href="css/style.css" rel="stylesheet">
            <style>
                @media print {
                    .btn { display: none !important; }
                    body { font-size: 12px; }
                }
            </style>
        </head>
        <body>
            <div class="container mt-3">
                <div class="text-center mb-4">
                    <h3>نظام المحاسبة للرعاة</h3>
                    <p class="text-muted">تاريخ الطباعة: ${new Date().toLocaleDateString('ar-YE')}</p>
                </div>
                ${reportContent}
            </div>
        </body>
        </html>
    `);

    printWindow.document.close();
    printWindow.print();
}
