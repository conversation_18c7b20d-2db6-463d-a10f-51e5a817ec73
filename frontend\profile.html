<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الملف الشخصي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container py-4">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">الملف الشخصي</h4>
                    </div>
                    <div class="card-body">
                        <form id="profileForm">
                            <div class="mb-3">
                                <label class="form-label">اسم المستخدم</label>
                                <input type="text" class="form-control" id="username" disabled>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">الاسم الكامل</label>
                                <input type="text" class="form-control" id="fullName">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email">
                            </div>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-save me-2"></i>حفظ التغييرات
                            </button>
                        </form>
                        
                        <hr>
                        
                        <h5>تغيير كلمة المرور</h5>
                        <form id="changePasswordForm" class="mt-3">
                            <div class="mb-3">
                                <label class="form-label">كلمة المرور الحالية</label>
                                <input type="password" class="form-control" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">كلمة المرور الجديدة</label>
                                <input type="password" class="form-control" required>
                            </div>
                            <button type="submit" class="btn btn-warning w-100">
                                <i class="fas fa-key me-2"></i>تغيير كلمة المرور
                            </button>
                        </form>
                        
                        <div class="text-center mt-4">
                            <a href="index.html" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-right me-2"></i>العودة للرئيسية
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/auth.js"></script>
    <script>
        // Load user profile data
        document.addEventListener('DOMContentLoaded', function() {
            if (!auth.isAuthenticated()) {
                window.location.href = 'login.html';
                return;
            }
            
            const user = auth.getUser();
            if (user) {
                document.getElementById('username').value = user.username || '';
                document.getElementById('fullName').value = user.name || '';
                document.getElementById('email').value = user.email || '';
            }
            
            // Profile form submission
            document.getElementById('profileForm').addEventListener('submit', async function(e) {
                e.preventDefault();
                // TODO: Implement profile update
                alert('سيتم تحديث الملف الشخصي قريبًا');
            });
            
            // Password change form submission
            document.getElementById('changePasswordForm').addEventListener('submit', async function(e) {
                e.preventDefault();
                // TODO: Implement password change
                alert('سيتم تغيير كلمة المرور قريبًا');
            });
        });
    </script>
</body>
</html>
