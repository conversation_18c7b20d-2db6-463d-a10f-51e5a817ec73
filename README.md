# نظام المحاسبة للرعاة
## Sponsors Accounting System

نظام محاسبي بسيط وفعال لإدارة الديون والحوالات المالية للرعاة باللغة العربية. يوفر واجهة مستخدم سهلة الاستخدام مع تقارير شاملة وإمكانيات طباعة متقدمة.

## 🌟 الميزات الرئيسية

### 👥 إدارة الرعاة
- إضافة وتعديل وحذف بيانات الرعاة
- تتبع معلومات الاتصال والعناوين
- عرض الأرصدة المالية لكل راعي

### 💰 إدارة الديون
- تسجيل الديون اليومية مع التواريخ والأوصاف
- ربط كل دين براعي محدد
- عرض تاريخ شامل للديون

### 💸 إدارة الحوالات
- تسجيل الحوالات المالية المستلمة
- تتبع مصادر الحوالات (البنوك، الحوالات النقدية)
- ربط كل حوالة براعي محدد

### 📊 التقارير المتقدمة
- **الملخص الإجمالي**: إحصائيات شاملة لجميع الرعاة
- **التقرير اليومي**: معاملات تاريخ محدد
- **تقرير الراعي**: تفاصيل شاملة لراعي محدد
- **إمكانية الطباعة**: جميع التقارير قابلة للطباعة

### 🎨 واجهة المستخدم
- تصميم عصري ومتجاوب باللغة العربية
- ألوان وأيقونات واضحة ومفهومة
- سهولة التنقل والاستخدام
- دعم الأجهزة المختلفة

## 🚀 التشغيل السريع

### الطريقة الأسهل:
```bash
# Windows
start_system.bat

# Linux/Mac
bash start_system.sh
```

### التشغيل اليدوي:
```bash
# 1. تثبيت المتطلبات
cd backend
pip install -r requirements.txt

# 2. إضافة بيانات تجريبية (اختياري)
python add_sample_data.py

# 3. تشغيل الخادم
python app.py

# 4. فتح المتصفح
# انتقل إلى: http://localhost:5000
```

## 🏗️ التقنيات المستخدمة

### Backend (الخادم الخلفي)
- **Python 3.x**: لغة البرمجة الأساسية
- **Flask**: إطار عمل الويب
- **SQLAlchemy**: للتعامل مع قاعدة البيانات
- **SQLite**: قاعدة البيانات المحلية

### Frontend (واجهة المستخدم)
- **HTML5 + CSS3**: هيكل وتنسيق الصفحات
- **JavaScript**: التفاعل والوظائف الديناميكية
- **Bootstrap 5**: التصميم المتجاوب
- **Font Awesome**: الأيقونات
- **Chart.js**: الرسوم البيانية (للتطوير المستقبلي)

## 📁 هيكل المشروع

```
accounting_system/
├── 📁 backend/                 # الخادم الخلفي
│   ├── app.py                 # التطبيق الرئيسي
│   ├── models.py              # نماذج قاعدة البيانات
│   ├── add_sample_data.py     # إضافة بيانات تجريبية
│   └── requirements.txt       # المكتبات المطلوبة
├── 📁 frontend/               # واجهة المستخدم
│   ├── index.html            # الصفحة الرئيسية
│   ├── 📁 css/
│   │   └── style.css         # التنسيقات
│   └── 📁 js/
│       └── app.js            # الوظائف الرئيسية
├── 📁 database/              # قاعدة البيانات
│   └── accounting.db         # ملف قاعدة البيانات
├── 📁 docs/                  # الوثائق
│   └── user_guide.md         # دليل المستخدم
├── start_system.bat          # تشغيل سريع (Windows)
├── start_system.sh           # تشغيل سريع (Linux/Mac)
└── README.md                 # هذا الملف
```

## 📊 البيانات التجريبية

يتضمن النظام بيانات تجريبية تشمل:
- **5 رعاة** مع معلومات كاملة
- **25 دين** موزعة على فترات مختلفة
- **15 حوالة** من مصادر متنوعة
- **إجمالي الديون**: 8,750,000 ريال يمني
- **إجمالي الحوالات**: 2,250,000 ريال يمني
- **الرصيد المتبقي**: 6,500,000 ريال يمني

## 🔧 المتطلبات التقنية

### متطلبات النظام:
- **Python 3.7+**
- **متصفح ويب حديث** (Chrome, Firefox, Safari, Edge)
- **ذاكرة**: 512 MB RAM كحد أدنى
- **مساحة القرص**: 100 MB

### المكتبات المطلوبة:
```
Flask==3.1.0
SQLAlchemy==2.0.40
Flask-CORS==4.0.0
python-dateutil==2.9.0
Flask-SQLAlchemy==3.1.1
```

## 📖 الوثائق

- **[دليل المستخدم](docs/user_guide.md)**: شرح مفصل لاستخدام النظام
- **[API Documentation](backend/app.py)**: توثيق واجهات البرمجة
- **[Database Schema](backend/models.py)**: هيكل قاعدة البيانات

## 🔄 التطوير المستقبلي

### الميزات المخطط إضافتها:
- [ ] نظام المستخدمين والصلاحيات
- [ ] التقارير الشهرية والسنوية
- [ ] تصدير البيانات إلى Excel/PDF
- [ ] نظام الإشعارات والتذكيرات
- [ ] واجهة الهاتف المحمول
- [ ] النسخ الاحتياطي التلقائي
- [ ] دعم عملات متعددة
- [ ] الرسوم البيانية التفاعلية

## 🐛 الإبلاغ عن المشاكل

إذا واجهت أي مشاكل:
1. تحقق من [دليل المستخدم](docs/user_guide.md)
2. راجع ملفات السجل في وحدة التحكم
3. تأكد من تثبيت جميع المتطلبات
4. أعد تشغيل النظام

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

## 👨‍💻 المطور

تم تطوير هذا النظام باستخدام **Augment Agent** - مساعد الذكي للبرمجة من Augment Code.

---

**🌟 نظام المحاسبة للرعاة - حل شامل وبسيط لإدارة المعاملات المالية**
