# -*- coding: utf-8 -*-
"""
التطبيق الرئيسي لنظام المحاسبة
Main Application for Accounting System
"""

from flask import Flask, jsonify, request, send_from_directory, g
from flask_cors import CORS
from models import db, Sponsor, Debt, Remittance, User
from datetime import datetime, date
from auth import token_required, admin_required
from auth_routes import auth_bp
import os
import jwt

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'accounting-system-secret-key-2024')
app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY', 'jwt-secret-key-2024')
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = 86400  # 24 hours

# إعداد قاعدة البيانات
basedir = os.path.abspath(os.path.dirname(__file__))
database_path = os.path.join(basedir, '..', 'database', 'accounting.db')
os.makedirs(os.path.dirname(database_path), exist_ok=True)

app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{database_path}'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# تهيئة قاعدة البيانات
db.init_app(app)

# تمكين CORS للسماح بالطلبات من المتصفح
CORS(app, supports_credentials=True)

# Register blueprints
app.register_blueprint(auth_bp)

# إنشاء الجداول عند بدء التطبيق
with app.app_context():
    db.create_all()

# خدمة الملفات الثابتة (Frontend)
@app.route('/')
def serve_frontend():
    """خدمة الصفحة الرئيسية"""
    frontend_path = os.path.join(basedir, '..', 'frontend')
    return send_from_directory(frontend_path, 'index.html')

@app.route('/<path:filename>')
def serve_static_files(filename):
    """خدمة الملفات الثابتة"""
    frontend_path = os.path.join(basedir, '..', 'frontend')
    return send_from_directory(frontend_path, filename)

# ===== Middleware for Authentication =====

@app.before_request
def before_request():
    """Process token before each request"""
    # Skip authentication for these routes
    if request.endpoint in ['serve_frontend', 'serve_static_files', 'login', 'register']:
        return
        
    # Skip OPTIONS requests (preflight)
    if request.method == 'OPTIONS':
        return
        
    # Check for token in Authorization header
    auth_header = request.headers.get('Authorization')
    if not auth_header:
        return jsonify({'message': 'Missing authorization token'}), 401
        
    try:
        token = auth_header.split(" ")[1]
        payload = jwt.decode(
            token,
            app.config['JWT_SECRET_KEY'],
            algorithms=['HS256']
        )
        g.current_user = User.query.get(payload['sub'])
    except (jwt.ExpiredSignatureError, jwt.InvalidTokenError) as e:
        return jsonify({'message': 'Invalid or expired token'}), 401
    except Exception as e:
        return jsonify({'message': 'Authentication failed'}), 401

# ===== API Routes للرعاة =====

@app.route('/api/sponsors', methods=['GET'])
@token_required
def get_sponsors():
    """الحصول على قائمة جميع الرعاة"""
    try:
        sponsors = Sponsor.query.all()
        return jsonify({
            'success': True,
            'data': [sponsor.to_dict() for sponsor in sponsors]
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/sponsors', methods=['POST'])
@token_required
def create_sponsor(current_user):
    """إنشاء راعي جديد"""
    try:
        data = request.get_json()
        
        if not data or not data.get('name'):
            return jsonify({'success': False, 'error': 'اسم الراعي مطلوب'}), 400
        
        sponsor = Sponsor(
            name=data['name'],
            phone=data.get('phone', ''),
            address=data.get('address', '')
        )
        
        db.session.add(sponsor)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم إنشاء الراعي بنجاح',
            'data': sponsor.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/sponsors/<int:sponsor_id>', methods=['GET'])
@token_required
def get_sponsor(current_user, sponsor_id):
    """الحصول على بيانات راعي محدد"""
    try:
        sponsor = Sponsor.query.get_or_404(sponsor_id)
        return jsonify({
            'success': True,
            'data': sponsor.to_dict()
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/sponsors/<int:sponsor_id>', methods=['PUT'])
@token_required
def update_sponsor(current_user, sponsor_id):
    """تحديث بيانات راعي"""
    try:
        sponsor = Sponsor.query.get_or_404(sponsor_id)
        data = request.get_json()
        
        if data.get('name'):
            sponsor.name = data['name']
        if 'phone' in data:
            sponsor.phone = data['phone']
        if 'address' in data:
            sponsor.address = data['address']
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم تحديث بيانات الراعي بنجاح',
            'data': sponsor.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/sponsors/<int:sponsor_id>', methods=['DELETE'])
@token_required
def delete_sponsor(current_user, sponsor_id):
    """حذف راعي"""
    try:
        sponsor = Sponsor.query.get_or_404(sponsor_id)
        db.session.delete(sponsor)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم حذف الراعي بنجاح'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

# ===== API Routes للديون =====

@app.route('/api/debts', methods=['GET'])
@token_required
def get_debts(current_user):
    """الحصول على قائمة جميع الديون"""
    try:
        sponsor_id = request.args.get('sponsor_id')
        if sponsor_id:
            debts = Debt.query.filter_by(sponsor_id=sponsor_id).order_by(Debt.date.desc()).all()
        else:
            debts = Debt.query.order_by(Debt.date.desc()).all()

        return jsonify({
            'success': True,
            'data': [debt.to_dict() for debt in debts]
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/debts', methods=['POST'])
@token_required
def create_debt(current_user):
    """إنشاء دين جديد"""
    try:
        data = request.get_json()

        required_fields = ['sponsor_id', 'amount', 'date']
        for field in required_fields:
            if not data or not data.get(field):
                return jsonify({'success': False, 'error': f'{field} مطلوب'}), 400

        # التحقق من وجود الراعي
        sponsor = Sponsor.query.get(data['sponsor_id'])
        if not sponsor:
            return jsonify({'success': False, 'error': 'الراعي غير موجود'}), 404

        debt = Debt(
            sponsor_id=data['sponsor_id'],
            amount=float(data['amount']),
            description=data.get('description', ''),
            date=datetime.strptime(data['date'], '%Y-%m-%d').date()
        )

        db.session.add(debt)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم إضافة الدين بنجاح',
            'data': debt.to_dict()
        }), 201

    except ValueError as e:
        return jsonify({'success': False, 'error': 'تنسيق التاريخ غير صحيح'}), 400
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/debts/<int:debt_id>', methods=['DELETE'])
@token_required
def delete_debt(current_user, debt_id):
    """حذف دين"""
    try:
        debt = Debt.query.get_or_404(debt_id)
        db.session.delete(debt)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم حذف الدين بنجاح'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

# ===== API Routes للحوالات =====

@app.route('/api/remittances', methods=['GET'])
@token_required
def get_remittances(current_user):
    """الحصول على قائمة جميع الحوالات"""
    try:
        sponsor_id = request.args.get('sponsor_id')
        if sponsor_id:
            remittances = Remittance.query.filter_by(sponsor_id=sponsor_id).order_by(Remittance.date.desc()).all()
        else:
            remittances = Remittance.query.order_by(Remittance.date.desc()).all()

        return jsonify({
            'success': True,
            'data': [remittance.to_dict() for remittance in remittances]
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/remittances', methods=['POST'])
@token_required
def create_remittance(current_user):
    """إنشاء حوالة جديدة"""
    try:
        data = request.get_json()

        required_fields = ['sponsor_id', 'amount', 'date']
        for field in required_fields:
            if not data or not data.get(field):
                return jsonify({'success': False, 'error': f'{field} مطلوب'}), 400

        # التحقق من وجود الراعي
        sponsor = Sponsor.query.get(data['sponsor_id'])
        if not sponsor:
            return jsonify({'success': False, 'error': 'الراعي غير موجود'}), 404

        remittance = Remittance(
            sponsor_id=data['sponsor_id'],
            amount=float(data['amount']),
            source=data.get('source', ''),
            date=datetime.strptime(data['date'], '%Y-%m-%d').date()
        )

        db.session.add(remittance)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم إضافة الحوالة بنجاح',
            'data': remittance.to_dict()
        }), 201

    except ValueError as e:
        return jsonify({'success': False, 'error': 'تنسيق التاريخ غير صحيح'}), 400
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/remittances/<int:remittance_id>', methods=['DELETE'])
@token_required
def delete_remittance(current_user, remittance_id):
    """حذف حوالة"""
    try:
        remittance = Remittance.query.get_or_404(remittance_id)
        db.session.delete(remittance)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'تم حذف الحوالة بنجاح'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

# ===== API Routes للتقارير =====

@app.route('/api/reports/summary', methods=['GET'])
@token_required
def get_summary_report(current_user):
    """تقرير الملخص الإجمالي مع إمكانية الفلترة"""
    try:
        from_date = request.args.get('from_date')
        to_date = request.args.get('to_date')

        sponsors = Sponsor.query.all()
        total_debts = 0
        total_remittances = 0
        total_balance = 0

        sponsors_data = []
        for sponsor in sponsors:
            # حساب الديون والحوالات مع الفلترة إذا وجدت
            if from_date or to_date:
                debts_query = Debt.query.filter_by(sponsor_id=sponsor.id)
                remittances_query = Remittance.query.filter_by(sponsor_id=sponsor.id)

                if from_date:
                    from_date_obj = datetime.strptime(from_date, '%Y-%m-%d').date()
                    debts_query = debts_query.filter(Debt.date >= from_date_obj)
                    remittances_query = remittances_query.filter(Remittance.date >= from_date_obj)

                if to_date:
                    to_date_obj = datetime.strptime(to_date, '%Y-%m-%d').date()
                    debts_query = debts_query.filter(Debt.date <= to_date_obj)
                    remittances_query = remittances_query.filter(Remittance.date <= to_date_obj)

                sponsor_debts = sum(float(debt.amount) for debt in debts_query.all())
                sponsor_remittances = sum(float(remittance.amount) for remittance in remittances_query.all())
            else:
                sponsor_debts = sponsor.get_total_debts()
                sponsor_remittances = sponsor.get_total_remittances()

            sponsor_balance = sponsor_debts - sponsor_remittances

            sponsors_data.append({
                'id': sponsor.id,
                'name': sponsor.name,
                'total_debts': sponsor_debts,
                'total_remittances': sponsor_remittances,
                'balance': sponsor_balance
            })

            total_debts += sponsor_debts
            total_remittances += sponsor_remittances
            total_balance += sponsor_balance

        return jsonify({
            'success': True,
            'data': {
                'sponsors': sponsors_data,
                'totals': {
                    'total_debts': total_debts,
                    'total_remittances': total_remittances,
                    'total_balance': total_balance,
                    'sponsors_count': len(sponsors)
                },
                'filter': {
                    'from_date': from_date,
                    'to_date': to_date
                }
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/reports/sponsor/<int:sponsor_id>', methods=['GET'])
@token_required
def get_sponsor_report(current_user, sponsor_id):
    """تقرير شامل لراعي محدد"""
    try:
        sponsor = Sponsor.query.get_or_404(sponsor_id)
        debts = Debt.query.filter_by(sponsor_id=sponsor_id).order_by(Debt.date.desc()).all()
        remittances = Remittance.query.filter_by(sponsor_id=sponsor_id).order_by(Remittance.date.desc()).all()

        return jsonify({
            'success': True,
            'data': {
                'sponsor': sponsor.to_dict(),
                'debts': [debt.to_dict() for debt in debts],
                'remittances': [remittance.to_dict() for remittance in remittances]
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/reports/daily', methods=['GET'])
@token_required
def get_daily_report(current_user):
    """التقرير اليومي"""
    try:
        date_str = request.args.get('date')
        if not date_str:
            report_date = date.today()
        else:
            report_date = datetime.strptime(date_str, '%Y-%m-%d').date()

        debts = Debt.query.filter_by(date=report_date).all()
        remittances = Remittance.query.filter_by(date=report_date).all()

        daily_debts_total = sum(float(debt.amount) for debt in debts)
        daily_remittances_total = sum(float(remittance.amount) for remittance in remittances)

        return jsonify({
            'success': True,
            'data': {
                'date': report_date.isoformat(),
                'debts': [debt.to_dict() for debt in debts],
                'remittances': [remittance.to_dict() for remittance in remittances],
                'totals': {
                    'daily_debts': daily_debts_total,
                    'daily_remittances': daily_remittances_total,
                    'net_change': daily_debts_total - daily_remittances_total
                }
            }
        })
    except ValueError:
        return jsonify({'success': False, 'error': 'تنسيق التاريخ غير صحيح'}), 400
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/reports/monthly', methods=['GET'])
@token_required
def get_monthly_report(current_user):
    """التقرير الشهري"""
    try:
        year = int(request.args.get('year', date.today().year))
        month = int(request.args.get('month', date.today().month))

        # الحصول على جميع المعاملات في الشهر المحدد
        debts = db.session.query(Debt).filter(
            db.extract('year', Debt.date) == year,
            db.extract('month', Debt.date) == month
        ).all()

        remittances = db.session.query(Remittance).filter(
            db.extract('year', Remittance.date) == year,
            db.extract('month', Remittance.date) == month
        ).all()

        monthly_debts_total = sum(float(debt.amount) for debt in debts)
        monthly_remittances_total = sum(float(remittance.amount) for remittance in remittances)

        return jsonify({
            'success': True,
            'data': {
                'year': year,
                'month': month,
                'debts': [debt.to_dict() for debt in debts],
                'remittances': [remittance.to_dict() for remittance in remittances],
                'totals': {
                    'monthly_debts': monthly_debts_total,
                    'monthly_remittances': monthly_remittances_total,
                    'net_change': monthly_debts_total - monthly_remittances_total
                }
            }
        })
    except ValueError:
        return jsonify({'success': False, 'error': 'قيم السنة أو الشهر غير صحيحة'}), 400
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/reports/yearly', methods=['GET'])
@token_required
def get_yearly_report(current_user):
    """التقرير السنوي"""
    try:
        year = int(request.args.get('year', date.today().year))

        # الحصول على جميع المعاملات في السنة المحددة
        debts = db.session.query(Debt).filter(
            db.extract('year', Debt.date) == year
        ).all()

        remittances = db.session.query(Remittance).filter(
            db.extract('year', Remittance.date) == year
        ).all()

        yearly_debts_total = sum(float(debt.amount) for debt in debts)
        yearly_remittances_total = sum(float(remittance.amount) for remittance in remittances)

        # تجميع البيانات حسب الشهر
        monthly_data = {}
        for month in range(1, 13):
            monthly_data[month] = {
                'month': month,
                'debts': 0,
                'remittances': 0,
                'net_change': 0
            }

        # حساب الديون الشهرية
        for debt in debts:
            month = debt.date.month
            monthly_data[month]['debts'] += float(debt.amount)

        # حساب الحوالات الشهرية
        for remittance in remittances:
            month = remittance.date.month
            monthly_data[month]['remittances'] += float(remittance.amount)

        # حساب صافي التغيير لكل شهر
        for month_data in monthly_data.values():
            month_data['net_change'] = month_data['debts'] - month_data['remittances']

        return jsonify({
            'success': True,
            'data': {
                'year': year,
                'debts': [debt.to_dict() for debt in debts],
                'remittances': [remittance.to_dict() for remittance in remittances],
                'monthly_breakdown': list(monthly_data.values()),
                'totals': {
                    'yearly_debts': yearly_debts_total,
                    'yearly_remittances': yearly_remittances_total,
                    'net_change': yearly_debts_total - yearly_remittances_total
                }
            }
        })
    except ValueError:
        return jsonify({'success': False, 'error': 'قيمة السنة غير صحيحة'}), 400
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/reports/remittances-period', methods=['GET'])
@token_required
def get_remittances_period_report(current_user):
    """تقرير الحوالات لفترة محددة"""
    try:
        from_date = request.args.get('from_date')
        to_date = request.args.get('to_date')

        if not from_date or not to_date:
            return jsonify({'success': False, 'error': 'يجب تحديد تاريخ البداية والنهاية'}), 400

        from_date_obj = datetime.strptime(from_date, '%Y-%m-%d').date()
        to_date_obj = datetime.strptime(to_date, '%Y-%m-%d').date()

        # الحصول على الحوالات في الفترة المحددة
        remittances = db.session.query(Remittance).filter(
            Remittance.date >= from_date_obj,
            Remittance.date <= to_date_obj
        ).order_by(Remittance.date.desc()).all()

        # تجميع الحوالات حسب الراعي
        sponsors_summary = {}
        sources_summary = {}
        total_amount = 0

        for remittance in remittances:
            sponsor_id = remittance.sponsor_id
            sponsor_name = remittance.sponsor.name
            amount = float(remittance.amount)
            source = remittance.source or 'غير محدد'

            # تجميع حسب الراعي
            if sponsor_id not in sponsors_summary:
                sponsors_summary[sponsor_id] = {
                    'sponsor_id': sponsor_id,
                    'sponsor_name': sponsor_name,
                    'total_amount': 0,
                    'remittances_count': 0,
                    'remittances': []
                }

            sponsors_summary[sponsor_id]['total_amount'] += amount
            sponsors_summary[sponsor_id]['remittances_count'] += 1
            sponsors_summary[sponsor_id]['remittances'].append(remittance.to_dict())

            # تجميع حسب المصدر
            if source not in sources_summary:
                sources_summary[source] = {
                    'source': source,
                    'total_amount': 0,
                    'remittances_count': 0
                }

            sources_summary[source]['total_amount'] += amount
            sources_summary[source]['remittances_count'] += 1

            total_amount += amount

        return jsonify({
            'success': True,
            'data': {
                'period': {
                    'from_date': from_date,
                    'to_date': to_date
                },
                'remittances': [remittance.to_dict() for remittance in remittances],
                'sponsors_summary': list(sponsors_summary.values()),
                'sources_summary': list(sources_summary.values()),
                'totals': {
                    'total_amount': total_amount,
                    'total_count': len(remittances),
                    'sponsors_count': len(sponsors_summary),
                    'sources_count': len(sources_summary)
                }
            }
        })
    except ValueError:
        return jsonify({'success': False, 'error': 'تنسيق التاريخ غير صحيح'}), 400
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/reports/debts-period', methods=['GET'])
@token_required
def get_debts_period_report(current_user):
    """تقرير الديون لفترة محددة"""
    try:
        from_date = request.args.get('from_date')
        to_date = request.args.get('to_date')

        if not from_date or not to_date:
            return jsonify({'success': False, 'error': 'يجب تحديد تاريخ البداية والنهاية'}), 400

        from_date_obj = datetime.strptime(from_date, '%Y-%m-%d').date()
        to_date_obj = datetime.strptime(to_date, '%Y-%m-%d').date()

        # الحصول على الديون في الفترة المحددة
        debts = db.session.query(Debt).filter(
            Debt.date >= from_date_obj,
            Debt.date <= to_date_obj
        ).order_by(Debt.date.desc()).all()

        # تجميع الديون حسب الراعي
        sponsors_summary = {}
        total_amount = 0

        for debt in debts:
            sponsor_id = debt.sponsor_id
            sponsor_name = debt.sponsor.name
            amount = float(debt.amount)

            # تجميع حسب الراعي
            if sponsor_id not in sponsors_summary:
                sponsors_summary[sponsor_id] = {
                    'sponsor_id': sponsor_id,
                    'sponsor_name': sponsor_name,
                    'total_amount': 0,
                    'debts_count': 0,
                    'debts': []
                }

            sponsors_summary[sponsor_id]['total_amount'] += amount
            sponsors_summary[sponsor_id]['debts_count'] += 1
            sponsors_summary[sponsor_id]['debts'].append(debt.to_dict())

            total_amount += amount

        return jsonify({
            'success': True,
            'data': {
                'period': {
                    'from_date': from_date,
                    'to_date': to_date
                },
                'debts': [debt.to_dict() for debt in debts],
                'sponsors_summary': list(sponsors_summary.values()),
                'totals': {
                    'total_amount': total_amount,
                    'total_count': len(debts),
                    'sponsors_count': len(sponsors_summary)
                }
            }
        })
    except ValueError:
        return jsonify({'success': False, 'error': 'تنسيق التاريخ غير صحيح'}), 400
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/reports/balance-period', methods=['GET'])
@token_required
def get_balance_period_report(current_user):
    """تقرير الرصيد المتبقي لكل راعي خلال فترة محددة"""
    try:
        from_date = request.args.get('from_date')
        to_date = request.args.get('to_date')

        if not from_date or not to_date:
            return jsonify({'success': False, 'error': 'يجب تحديد تاريخ البداية والنهاية'}), 400

        from_date_obj = datetime.strptime(from_date, '%Y-%m-%d').date()
        to_date_obj = datetime.strptime(to_date, '%Y-%m-%d').date()

        # الحصول على جميع الرعاة
        sponsors = Sponsor.query.all()
        sponsors_balance = []

        total_debts = 0
        total_remittances = 0
        total_balance = 0

        for sponsor in sponsors:
            # حساب الديون في الفترة المحددة
            period_debts = db.session.query(Debt).filter(
                Debt.sponsor_id == sponsor.id,
                Debt.date >= from_date_obj,
                Debt.date <= to_date_obj
            ).all()

            # حساب الحوالات في الفترة المحددة
            period_remittances = db.session.query(Remittance).filter(
                Remittance.sponsor_id == sponsor.id,
                Remittance.date >= from_date_obj,
                Remittance.date <= to_date_obj
            ).all()

            sponsor_debts = sum(float(debt.amount) for debt in period_debts)
            sponsor_remittances = sum(float(remittance.amount) for remittance in period_remittances)
            sponsor_balance = sponsor_debts - sponsor_remittances

            # إضافة الراعي فقط إذا كان له معاملات في الفترة
            if sponsor_debts > 0 or sponsor_remittances > 0:
                sponsors_balance.append({
                    'sponsor_id': sponsor.id,
                    'sponsor_name': sponsor.name,
                    'sponsor_phone': sponsor.phone,
                    'sponsor_address': sponsor.address,
                    'period_debts': sponsor_debts,
                    'period_remittances': sponsor_remittances,
                    'period_balance': sponsor_balance,
                    'debts_count': len(period_debts),
                    'remittances_count': len(period_remittances),
                    'status': 'مدين' if sponsor_balance > 0 else 'دائن' if sponsor_balance < 0 else 'متوازن'
                })

                total_debts += sponsor_debts
                total_remittances += sponsor_remittances
                total_balance += sponsor_balance

        # ترتيب الرعاة حسب الرصيد المتبقي (الأعلى أولاً)
        sponsors_balance.sort(key=lambda x: x['period_balance'], reverse=True)

        # تصنيف الرعاة
        debtors = [s for s in sponsors_balance if s['period_balance'] > 0]
        creditors = [s for s in sponsors_balance if s['period_balance'] < 0]
        balanced = [s for s in sponsors_balance if s['period_balance'] == 0]

        return jsonify({
            'success': True,
            'data': {
                'period': {
                    'from_date': from_date,
                    'to_date': to_date
                },
                'sponsors_balance': sponsors_balance,
                'categories': {
                    'debtors': debtors,
                    'creditors': creditors,
                    'balanced': balanced
                },
                'totals': {
                    'total_debts': total_debts,
                    'total_remittances': total_remittances,
                    'total_balance': total_balance,
                    'sponsors_count': len(sponsors_balance),
                    'debtors_count': len(debtors),
                    'creditors_count': len(creditors),
                    'balanced_count': len(balanced)
                }
            }
        })
    except ValueError:
        return jsonify({'success': False, 'error': 'تنسيق التاريخ غير صحيح'}), 400
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# معالج الأخطاء
@app.errorhandler(404)
def not_found(error):
    return jsonify({'success': False, 'error': 'الصفحة غير موجودة'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'success': False, 'error': 'خطأ في الخادم'}), 500

if __name__ == '__main__':
    print("🚀 بدء تشغيل نظام المحاسبة...")
    print("📊 النظام متاح على: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
