"""
Authentication Routes

This module contains the authentication API endpoints for user registration, login, and account management.
"""

from flask import Blueprint, request, jsonify, current_app
from models import db, User
from auth import generate_token, token_required
from datetime import datetime

# Create Blueprint for auth routes
auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/api/auth/register', methods=['POST'])
def register():
    """Register a new user"""
    data = request.get_json()
    
    # Validate required fields
    if not all(k in data for k in ['username', 'password', 'name']):
        return jsonify({'message': 'Missing required fields'}), 400
    
    # Check if username already exists
    if User.query.filter_by(username=data['username']).first():
        return jsonify({'message': 'Username already exists'}), 400
    
    # Create new user
    user = User(
        username=data['username'],
        name=data['name'],
        is_admin=data.get('is_admin', False)
    )
    user.set_password(data['password'])
    
    try:
        db.session.add(user)
        db.session.commit()
        
        # Generate token for the new user
        token = generate_token(user.id)
        
        return jsonify({
            'message': 'User registered successfully',
            'user': user.to_dict(),
            'token': token
        }), 201
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'Error registering user: {str(e)}')
        return jsonify({'message': 'Error registering user'}), 500

@auth_bp.route('/api/auth/login', methods=['POST'])
def login():
    """User login"""
    data = request.get_json()
    
    # Validate required fields
    if not all(k in data for k in ['username', 'password']):
        return jsonify({'message': 'Username and password are required'}), 400
    
    # Find user by username
    user = User.query.filter_by(username=data['username']).first()
    
    # Check if user exists and password is correct
    if not user or not user.check_password(data['password']):
        return jsonify({'message': 'Invalid username or password'}), 401
    
    # Update last login time
    user.last_login = datetime.utcnow()
    db.session.commit()
    
    # Generate token
    token = generate_token(user.id)
    
    return jsonify({
        'message': 'Login successful',
        'user': user.to_dict(),
        'token': token
    })

@auth_bp.route('/api/auth/me', methods=['GET'])
@token_required
def get_current_user(current_user):
    """Get current user's profile"""
    return jsonify(current_user.to_dict())

@auth_bp.route('/api/auth/change-password', methods=['POST'])
@token_required
def change_password(current_user):
    """Change user's password"""
    data = request.get_json()
    
    if not all(k in data for k in ['current_password', 'new_password']):
        return jsonify({'message': 'Current and new password are required'}), 400
    
    # Verify current password
    if not current_user.check_password(data['current_password']):
        return jsonify({'message': 'Current password is incorrect'}), 400
    
    # Update to new password
    current_user.set_password(data['new_password'])
    db.session.commit()
    
    return jsonify({'message': 'Password updated successfully'})

@auth_bp.route('/api/auth/users', methods=['GET'])
@token_required
def get_users(current_user):
    """Get all users (admin only)"""
    if not current_user.is_admin:
        return jsonify({'message': 'Admin access required'}), 403
    
    users = User.query.all()
    return jsonify([user.to_dict() for user in users])

@auth_bp.route('/api/auth/users/<int:user_id>', methods=['PUT'])
@token_required
def update_user(current_user, user_id):
    """Update user (admin only)"""
    if not current_user.is_admin:
        return jsonify({'message': 'Admin access required'}), 403
    
    user = User.query.get_or_404(user_id)
    data = request.get_json()
    
    if 'username' in data and data['username'] != user.username:
        if User.query.filter_by(username=data['username']).first():
            return jsonify({'message': 'Username already exists'}), 400
        user.username = data['username']
    
    if 'name' in data:
        user.name = data['name']
    
    if 'is_admin' in data and current_user.id != user_id:  # Prevent self-demotion
        user.is_admin = data['is_admin']
    
    db.session.commit()
    
    return jsonify(user.to_dict())

@auth_bp.route('/api/auth/users/<int:user_id>', methods=['DELETE'])
@token_required
def delete_user(current_user, user_id):
    """Delete user (admin only)"""
    if not current_user.is_admin:
        return jsonify({'message': 'Admin access required'}), 403
    
    if current_user.id == user_id:
        return jsonify({'message': 'Cannot delete your own account'}), 400
    
    user = User.query.get_or_404(user_id)
    
    db.session.delete(user)
    db.session.commit()
    
    return jsonify({'message': 'User deleted successfully'})
