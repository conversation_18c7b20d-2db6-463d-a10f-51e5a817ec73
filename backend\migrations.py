"""
Database Migration Script

This script handles database migrations for the Accounting System.
Run this script after making changes to the database models.
"""

import os
import sys
from datetime import datetime
from sqlalchemy import create_engine, MetaData, Table, Column, Integer, String, Boolean, DateTime, Numeric, ForeignKey, text
from sqlalchemy.orm import sessionmaker

# Add the parent directory to the path so we can import our models
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from backend.models import db
from backend.app import app

def create_migration_table(engine):
    """Create the migrations table if it doesn't exist"""
    with engine.connect() as conn:
        conn.execute("""
        CREATE TABLE IF NOT EXISTS migrations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """)
        conn.commit()

def get_applied_migrations(engine):
    """Get list of already applied migrations"""
    with engine.connect() as conn:
        result = conn.execute("SELECT name FROM migrations")
        return [row[0] for row in result]

def add_migration(conn, name):
    """Add a migration record"""
    conn.execute(
        "INSERT INTO migrations (name, applied_at) VALUES (:name, :applied_at)",
        {"name": name, "applied_at": datetime.utcnow()}
    )
    conn.commit()

def run_migrations():
    """Run all pending migrations"""
    print("🚀 Starting database migrations...")
    
    # Set up database connection
    database_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'database', 'accounting.db')
    engine = create_engine(f'sqlite:///{database_path}')
    
    # Create migrations table if it doesn't exist
    create_migration_table(engine)
    
    # Get list of applied migrations
    applied_migrations = get_applied_migrations(engine)
    
    # Start a connection for transactions
    with engine.connect() as conn:
        # Migration 1: Add users table and update existing tables with user tracking
        if '001_initial_user_schema' not in applied_migrations:
            print("🔄 Applying migration: 001_initial_user_schema")
            
            # Create users table
            conn.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL UNIQUE,
                password_hash TEXT NOT NULL,
                name TEXT NOT NULL,
                is_admin BOOLEAN DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP
            )
            """)
            
            # Add user tracking columns to debts table
            conn.execute("""
            ALTER TABLE debts 
            ADD COLUMN created_by_id INTEGER REFERENCES users(id),
            ADD COLUMN updated_by_id INTEGER REFERENCES users(id),
            ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            """)
            
            # Add user tracking columns to remittances table
            conn.execute("""
            ALTER TABLE remittances 
            ADD COLUMN created_by_id INTEGER REFERENCES users(id),
            ADD COLUMN updated_by_id INTEGER REFERENCES users(id),
            ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            """)
            
            # Create indexes for better performance
            conn.execute("CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_debts_created_by ON debts(created_by_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_remittances_created_by ON remittances(created_by_id)")
            
            # Add default admin user if no users exist
            result = conn.execute("SELECT COUNT(*) FROM users").scalar()
            if result == 0:
                from werkzeug.security import generate_password_hash
                admin_password = os.getenv('ADMIN_PASSWORD', 'admin123')
                hashed_password = generate_password_hash(admin_password)
                
                conn.execute(
                    """
                    INSERT INTO users (username, password_hash, name, is_admin, created_at)
                    VALUES (:username, :password, :name, 1, :created_at)
                    """,
                    {
                        "username": "admin",
                        "password": hashed_password,
                        "name": "مدير النظام",
                        "created_at": datetime.utcnow()
                    }
                )
                print("✅ Created default admin user (username: admin, password: admin123)")
            
            # Record migration as completed
            add_migration(conn, '001_initial_user_schema')
            print("✅ Migration 001_initial_user_schema completed successfully")
        
        # Add more migrations here as needed
        
        print("✨ All migrations completed successfully!")

if __name__ == '__main__':
    with app.app_context():
        run_migrations()
