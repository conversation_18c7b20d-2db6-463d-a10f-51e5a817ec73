// نظام المحاسبة للرعاة - تحسينات الأداء
// Performance Optimizations for Accounting System

// إعدادات الأداء
const performanceSettings = {
    virtualScrolling: {
        enabled: true,
        itemHeight: 60,
        bufferSize: 10
    },
    lazyLoading: {
        enabled: true,
        threshold: 0.1
    },
    caching: {
        enabled: true,
        maxAge: 5 * 60 * 1000, // 5 دقائق
        maxSize: 50
    }
};

// ذاكرة التخزين المؤقت
const cache = new Map();

// تحسين عرض الجداول الكبيرة باستخدام Virtual Scrolling
class VirtualTable {
    constructor(container, data, renderItem, itemHeight = 60) {
        this.container = container;
        this.data = data;
        this.renderItem = renderItem;
        this.itemHeight = itemHeight;
        this.visibleStart = 0;
        this.visibleEnd = 0;
        this.scrollTop = 0;
        
        this.init();
    }
    
    init() {
        this.container.style.position = 'relative';
        this.container.style.overflow = 'auto';
        this.container.style.height = '400px'; // ارتفاع ثابت
        
        this.viewport = document.createElement('div');
        this.viewport.style.position = 'absolute';
        this.viewport.style.top = '0';
        this.viewport.style.left = '0';
        this.viewport.style.right = '0';
        
        this.spacer = document.createElement('div');
        this.spacer.style.height = (this.data.length * this.itemHeight) + 'px';
        
        this.container.appendChild(this.spacer);
        this.container.appendChild(this.viewport);
        
        this.container.addEventListener('scroll', this.onScroll.bind(this));
        this.render();
    }
    
    onScroll() {
        this.scrollTop = this.container.scrollTop;
        this.render();
    }
    
    render() {
        const containerHeight = this.container.clientHeight;
        const startIndex = Math.floor(this.scrollTop / this.itemHeight);
        const endIndex = Math.min(
            startIndex + Math.ceil(containerHeight / this.itemHeight) + performanceSettings.virtualScrolling.bufferSize,
            this.data.length
        );
        
        this.visibleStart = startIndex;
        this.visibleEnd = endIndex;
        
        // مسح المحتوى السابق
        this.viewport.innerHTML = '';
        
        // إنشاء العناصر المرئية
        for (let i = startIndex; i < endIndex; i++) {
            const item = this.renderItem(this.data[i], i);
            item.style.position = 'absolute';
            item.style.top = (i * this.itemHeight) + 'px';
            item.style.left = '0';
            item.style.right = '0';
            item.style.height = this.itemHeight + 'px';
            
            this.viewport.appendChild(item);
        }
    }
    
    updateData(newData) {
        this.data = newData;
        this.spacer.style.height = (this.data.length * this.itemHeight) + 'px';
        this.render();
    }
}

// تحميل البيانات بشكل تدريجي (Lazy Loading)
class LazyLoader {
    constructor() {
        this.observer = new IntersectionObserver(
            this.handleIntersection.bind(this),
            {
                threshold: performanceSettings.lazyLoading.threshold
            }
        );
    }
    
    observe(element, loadCallback) {
        element.dataset.loadCallback = loadCallback.toString();
        this.observer.observe(element);
    }
    
    handleIntersection(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;
                const callbackStr = element.dataset.loadCallback;
                
                if (callbackStr) {
                    try {
                        const callback = new Function('return ' + callbackStr)();
                        callback();
                        this.observer.unobserve(element);
                    } catch (error) {
                        console.error('خطأ في تحميل البيانات:', error);
                    }
                }
            }
        });
    }
}

// إدارة ذاكرة التخزين المؤقت
class CacheManager {
    constructor() {
        this.cache = new Map();
        this.timestamps = new Map();
    }
    
    set(key, value) {
        if (!performanceSettings.caching.enabled) return;
        
        // إزالة العناصر القديمة إذا تجاوز الحد الأقصى
        if (this.cache.size >= performanceSettings.caching.maxSize) {
            const oldestKey = this.getOldestKey();
            if (oldestKey) {
                this.delete(oldestKey);
            }
        }
        
        this.cache.set(key, value);
        this.timestamps.set(key, Date.now());
    }
    
    get(key) {
        if (!performanceSettings.caching.enabled) return null;
        
        const timestamp = this.timestamps.get(key);
        if (!timestamp) return null;
        
        // التحقق من انتهاء صلاحية البيانات
        if (Date.now() - timestamp > performanceSettings.caching.maxAge) {
            this.delete(key);
            return null;
        }
        
        return this.cache.get(key);
    }
    
    delete(key) {
        this.cache.delete(key);
        this.timestamps.delete(key);
    }
    
    clear() {
        this.cache.clear();
        this.timestamps.clear();
    }
    
    getOldestKey() {
        let oldestKey = null;
        let oldestTime = Infinity;
        
        for (const [key, timestamp] of this.timestamps) {
            if (timestamp < oldestTime) {
                oldestTime = timestamp;
                oldestKey = key;
            }
        }
        
        return oldestKey;
    }
}

// مثيل مدير التخزين المؤقت
const cacheManager = new CacheManager();

// مثيل محمل البيانات التدريجي
const lazyLoader = new LazyLoader();

// تحسين طلبات API
async function optimizedFetch(url, options = {}) {
    const cacheKey = url + JSON.stringify(options);
    
    // التحقق من التخزين المؤقت أولاً
    const cachedData = cacheManager.get(cacheKey);
    if (cachedData) {
        console.log('تم تحميل البيانات من التخزين المؤقت:', url);
        return cachedData;
    }
    
    try {
        const response = await fetch(url, options);
        const data = await response.json();
        
        // حفظ في التخزين المؤقت
        cacheManager.set(cacheKey, data);
        
        return data;
    } catch (error) {
        console.error('خطأ في طلب البيانات:', error);
        throw error;
    }
}

// تحسين عرض الرسوم البيانية
function optimizedChartRender(chartFunction, data, delay = 100) {
    // استخدام requestAnimationFrame لتحسين الأداء
    return new Promise(resolve => {
        setTimeout(() => {
            requestAnimationFrame(() => {
                chartFunction(data);
                resolve();
            });
        }, delay);
    });
}

// تجميع العمليات (Batching)
class OperationBatcher {
    constructor(batchSize = 10, delay = 100) {
        this.batchSize = batchSize;
        this.delay = delay;
        this.queue = [];
        this.timer = null;
    }
    
    add(operation) {
        this.queue.push(operation);
        
        if (this.queue.length >= this.batchSize) {
            this.flush();
        } else if (!this.timer) {
            this.timer = setTimeout(() => this.flush(), this.delay);
        }
    }
    
    flush() {
        if (this.timer) {
            clearTimeout(this.timer);
            this.timer = null;
        }
        
        if (this.queue.length === 0) return;
        
        const batch = this.queue.splice(0);
        this.processBatch(batch);
    }
    
    processBatch(operations) {
        // معالجة العمليات في دفعة واحدة
        requestAnimationFrame(() => {
            operations.forEach(operation => {
                try {
                    operation();
                } catch (error) {
                    console.error('خطأ في معالجة العملية:', error);
                }
            });
        });
    }
}

// مثيل مجمع العمليات
const operationBatcher = new OperationBatcher();

// تحسين تحديث DOM
function optimizedDOMUpdate(element, content) {
    operationBatcher.add(() => {
        element.innerHTML = content;
    });
}

// مراقب الأداء
class PerformanceMonitor {
    constructor() {
        this.metrics = {
            loadTimes: [],
            renderTimes: [],
            memoryUsage: []
        };
    }
    
    startTimer(name) {
        performance.mark(`${name}-start`);
    }
    
    endTimer(name) {
        performance.mark(`${name}-end`);
        performance.measure(name, `${name}-start`, `${name}-end`);
        
        const measure = performance.getEntriesByName(name)[0];
        if (measure) {
            this.metrics.loadTimes.push({
                name: name,
                duration: measure.duration,
                timestamp: Date.now()
            });
        }
    }
    
    getMetrics() {
        return {
            ...this.metrics,
            memory: this.getMemoryUsage()
        };
    }
    
    getMemoryUsage() {
        if (performance.memory) {
            return {
                used: performance.memory.usedJSHeapSize,
                total: performance.memory.totalJSHeapSize,
                limit: performance.memory.jsHeapSizeLimit
            };
        }
        return null;
    }
    
    logPerformance() {
        const metrics = this.getMetrics();
        console.group('📊 إحصائيات الأداء');
        console.log('أوقات التحميل:', metrics.loadTimes);
        console.log('استخدام الذاكرة:', metrics.memory);
        console.groupEnd();
    }
}

// مثيل مراقب الأداء
const performanceMonitor = new PerformanceMonitor();

// تهيئة تحسينات الأداء
function initializePerformanceOptimizations() {
    // مراقبة استخدام الذاكرة
    if (performanceSettings.caching.enabled) {
        setInterval(() => {
            const memory = performanceMonitor.getMemoryUsage();
            if (memory && memory.used > memory.limit * 0.8) {
                console.warn('استخدام الذاكرة مرتفع، يتم مسح التخزين المؤقت...');
                cacheManager.clear();
            }
        }, 30000); // كل 30 ثانية
    }
    
    // تسجيل إحصائيات الأداء في وضع التطوير
    if (window.location.hostname === 'localhost') {
        setInterval(() => {
            performanceMonitor.logPerformance();
        }, 60000); // كل دقيقة
    }
}

// تصدير الوظائف للاستخدام العام
window.performanceOptimizations = {
    VirtualTable,
    LazyLoader,
    CacheManager,
    optimizedFetch,
    optimizedChartRender,
    optimizedDOMUpdate,
    performanceMonitor,
    cacheManager,
    lazyLoader
};

// تهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializePerformanceOptimizations();
});
